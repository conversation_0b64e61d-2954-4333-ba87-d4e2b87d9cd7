import * as jsYaml from 'js-yaml';

/**
 * Get the current tenant from localStorage or use default
 * @returns Current tenant name
 */
export function getCurrentTenant(): string {
  // For now, we'll use a simple approach. In a real application, this would
  // be determined by authentication, URL, or other mechanisms
  return localStorage.getItem('current-tenant') || 'default';
}

/**
 * Set the current tenant in localStorage
 * @param tenant Tenant name to set as current
 */
export function setCurrentTenant(tenant: string): void {
  localStorage.setItem('current-tenant', tenant);
}

/**
 * Generic utility for loading any YAML configuration
 * @param configPath Path to the YAML configuration file
 * @returns Parsed configuration object
 */
export async function loadConfig<T>(configPath: string): Promise<T> {
  try {
    console.log(`Attempting to load config from: ${configPath}`);
    const response = await fetch(configPath);
    if (!response.ok) {
      throw new Error(`Failed to load config from ${configPath}: ${response.statusText}`);
    }
    
    const yamlText = await response.text();
    console.log(`YAML content loaded, length: ${yamlText.length} characters`);
    const config = jsYaml.load(yamlText) as T;
    
    return config;
  } catch (error) {
    console.error(`Error loading config from ${configPath}:`, error);
    throw error;
  }
}

/**
 * Get the base path for tenant-specific configurations
 * @returns Path to the tenant's configuration directory
 */
export function getTenantBasePath(): string {
  const tenant = getCurrentTenant();
  return `/static/data/tenants/${tenant}`;
}

// Specific loaders for different configuration types
export async function loadMetricsConfig<T>(): Promise<T> {
  const tenant = getCurrentTenant();
  return loadConfig<T>(`/static/data/tenants/${tenant}/config/metrics.yaml`);
}

export async function loadAlertsConfig<T>(): Promise<T> {
  const tenant = getCurrentTenant();
  return loadConfig<T>(`/static/data/tenants/${tenant}/config/alerts.yaml`);
}

export async function loadFactoryConfig<T>(): Promise<T> {
  const tenant = getCurrentTenant();
  const configPath = `/static/data/tenants/${tenant}/factory/factoryLayout.yaml`;
  console.log(`Loading factory config from: ${configPath}`);
  return loadConfig<T>(configPath);
}

// Type definition for System Status items
export type StatusIndicator = "ok" | "warning" | "error";

export interface SystemStatusItem {
  id: string;
  name: string;
  icon: string; // Lucide icon name
  statusType: "indicator" | "progress";
  status?: StatusIndicator; // Only for indicator type
  value?: number; // Only for progress type
  tooltip: string;
}

export async function loadSystemStatusConfig(): Promise<{ items: SystemStatusItem[] }> {
  const tenant = getCurrentTenant();
  return loadConfig<{ items: SystemStatusItem[] }>(`/static/data/tenants/${tenant}/config/systemStatus.yaml`);
}

// Type definitions for KPI configuration
interface KPIValue {
  value: number;
  tooltip: string;
  unit?: string;
}
interface KPIWorkers {
  active: number;
  total: number;
  tooltip: string;
}
interface KPIConfig {
  equipmentEfficiency: KPIValue;
  workers: KPIWorkers;
  productionRate: KPIValue;
}

export async function loadKpiConfig(): Promise<{ kpis: KPIConfig }> {
  const tenant = getCurrentTenant();
  return loadConfig<{ kpis: KPIConfig }>(`/static/data/tenants/${tenant}/config/kpi.yaml`);
}

// Type definitions for Incident Management
interface IncidentUpdate {
  timestamp: string;
  author: string;
  note: string;
}

export interface Incident {
  id: string;
  title: string;
  description: string;
  type?: "process" | "safety" | "maintenance" | "quality";
  status: "open" | "in-progress" | "resolved";
  severity: "low" | "medium" | "high" | "critical";
  assignedTeam: string;
  reportedAt: string;
  resolvedAt: string | null;
  resolutionNotes: string | null;
  updates: IncidentUpdate[];
  media?: {
    videos?: {
      url: string;
      title: string;
      description?: string;
      thumbnail?: string;
    }[];
    images?: {
      url: string;
      title: string;
      description?: string;
    }[];
    documents?: {
      url: string;
      title: string;
      type: string;
    }[];
  };
}

export async function loadIncidentsConfig(): Promise<{ incidents: Incident[] }> {
  const tenant = getCurrentTenant();
  // Important: Handle potential 404 if the file doesn't exist for a tenant
  try {
    const config = await loadConfig<{ incidents: Incident[] }>(`/static/data/tenants/${tenant}/config/incidents.yaml`);
    // Ensure 'incidents' is always an array, even if YAML is empty or malformed slightly
    return { incidents: Array.isArray(config?.incidents) ? config.incidents : [] };
  } catch (error: any) {
    // If the file is not found (404), return empty array, otherwise re-throw
    if (error.message && error.message.includes('404')) {
      console.warn(`Incidents config not found for tenant '${tenant}'. Returning empty list.`);
      return { incidents: [] }; 
    }
    console.error(`Error loading incidents config for tenant '${tenant}':`, error);
    throw error; // Re-throw other errors
  }
}

// Re-added Navigation Config
export interface NavigationItem {
  title: string;
  icon: string; // Name of the lucide icon
  href: string;
}

export async function loadNavigationConfig(): Promise<{ items: NavigationItem[] }> {
  const tenant = getCurrentTenant();
  // Handle potential 404 for navigation config as well
  try {
    const config = await loadConfig<{ items: NavigationItem[] }>(`/static/data/tenants/${tenant}/config/navigation.yaml`);
    // Ensure 'items' is always an array
    return { items: Array.isArray(config?.items) ? config.items : [] };
  } catch (error: any) {
    if (error.message && error.message.includes('404')) {
      console.warn(`Navigation config not found for tenant '${tenant}'. Returning empty list.`);
      return { items: [] }; 
    }
    console.error(`Error loading navigation config for tenant '${tenant}':`, error);
    throw error;
  }
}
