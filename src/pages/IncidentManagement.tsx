import React, { useState, useRef } from 'react';
import { useQuery } from '@tanstack/react-query';
import { loadIncidentsConfig, Incident } from '@/utils/configLoader';
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";
import { AlertTriangle, Loader2, Bot, Calendar, User, Clock, CheckCircle, AlertCircle, Info, Shield, Settings, Zap } from 'lucide-react';
import { format, parseISO } from 'date-fns';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { ScrollArea } from "@/components/ui/scroll-area";
import AIResolutionModal from '@/components/AIResolutionModal';

const getSeverityIcon = (severity: string) => {
  switch (severity) {
    case 'critical': return <AlertTriangle className="h-4 w-4 text-red-500" />;
    case 'high': return <AlertCircle className="h-4 w-4 text-orange-500" />;
    case 'medium': return <Info className="h-4 w-4 text-blue-500" />;
    case 'low': return <CheckCircle className="h-4 w-4 text-green-500" />;
    default: return <Info className="h-4 w-4 text-gray-500" />;
  }
};

// Helper functions for incident type handling
const getTypeIcon = (type: string) => {
  switch (type) {
    case 'safety': return <Shield className="h-4 w-4 text-red-600" />;
    case 'process': return <Settings className="h-4 w-4 text-blue-600" />;
    case 'maintenance': return <Zap className="h-4 w-4 text-orange-600" />;
    case 'quality': return <CheckCircle className="h-4 w-4 text-green-600" />;
    default: return <AlertTriangle className="h-4 w-4 text-gray-600" />;
  }
};

const getTypeBadgeVariant = (type: string): "default" | "secondary" | "destructive" | "outline" => {
  switch (type) {
    case 'safety': return 'destructive';
    case 'process': return 'default';
    case 'maintenance': return 'secondary';
    case 'quality': return 'outline';
    default: return 'outline';
  }
};

const getTypeColor = (type: string) => {
  switch (type) {
    case 'safety': return 'text-red-600';
    case 'process': return 'text-blue-600';
    case 'maintenance': return 'text-orange-600';
    case 'quality': return 'text-green-600';
    default: return 'text-gray-600';
  }
};

const getSeverityBadgeVariant = (severity: string): "default" | "secondary" | "destructive" | "outline" => {
  switch (severity) {
    case 'critical': return 'destructive';
    case 'high': return 'secondary'; 
    case 'medium': return 'default'; 
    case 'low': return 'outline';
    default: return 'outline';
  }
};

const getStatusBadgeVariant = (status: string): "default" | "secondary" | "destructive" | "outline" => {
  switch (status) {
    case 'resolved': return 'default';
    case 'in-progress': return 'secondary';
    case 'open': return 'destructive';
    default: return 'outline';
  }
};

const getSeverityColor = (severity: string, incidentType?: string) => {
  // Special styling for safety incidents
  if (incidentType === 'safety') {
    switch (severity) {
      case 'critical': return 'border-l-red-600 bg-red-100 dark:bg-red-950/30 border-2 border-red-200 dark:border-red-800';
      case 'high': return 'border-l-red-500 bg-red-50 dark:bg-red-950/20 border border-red-200 dark:border-red-800';
      case 'medium': return 'border-l-orange-500 bg-orange-50 dark:bg-orange-950/20 border border-orange-200 dark:border-orange-800';
      case 'low': return 'border-l-yellow-500 bg-yellow-50 dark:bg-yellow-950/20 border border-yellow-200 dark:border-yellow-800';
      default: return 'border-l-red-500 bg-red-50 dark:bg-red-950/20 border border-red-200 dark:border-red-800';
    }
  }
  
  // Standard styling for other incident types
  switch (severity) {
    case 'critical': return 'border-l-red-500 bg-red-50 dark:bg-red-950/20';
    case 'high': return 'border-l-orange-500 bg-orange-50 dark:bg-orange-950/20';
    case 'medium': return 'border-l-blue-500 bg-blue-50 dark:bg-blue-950/20';
    case 'low': return 'border-l-green-500 bg-green-50 dark:bg-green-950/20';
    default: return 'border-l-gray-500 bg-gray-50 dark:bg-gray-950/20';
  }
};

const IncidentManagement: React.FC = () => {
  const [selectedIncident, setSelectedIncident] = useState<Incident | null>(null);
  const [filterStatus, setFilterStatus] = useState<string>('all');
  const [filterSeverity, setFilterSeverity] = useState<string>('all');
  const [filterType, setFilterType] = useState<string>('all');
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [isAIModalOpen, setIsAIModalOpen] = useState(false);
  const [openAccordion, setOpenAccordion] = useState<string>('');
  
  // Ref for the scrollable incidents container
  const incidentsScrollRef = useRef<HTMLDivElement>(null);
  const accordionRefs = useRef<{ [key: string]: HTMLDivElement | null }>({});

  const { data: incidentsData, isLoading, error } = useQuery<{ incidents: Incident[] }, Error>({ 
    queryKey: ['incidentsConfig'], 
    queryFn: loadIncidentsConfig
  });

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
      </div>
    );
  }

  if (error || !incidentsData) {
    return (
      <Card className="mt-4">
        <CardContent className="p-6 text-center text-destructive">
          <AlertTriangle className="h-8 w-8 mx-auto mb-2" />
          <p>Error loading incidents: {error?.message || "Could not load data."}</p>
        </CardContent>
      </Card>
    );
  }

  const filteredIncidents = incidentsData.incidents.filter(incident => {
    const matchesStatus = filterStatus === 'all' || incident.status === filterStatus;
    const matchesSeverity = filterSeverity === 'all' || incident.severity === filterSeverity;
    const matchesType = filterType === 'all' || incident.type === filterType;
    const matchesSearch = searchTerm === '' || 
                          incident.id.toLowerCase().includes(searchTerm.toLowerCase()) ||
                          incident.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                          incident.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                          incident.assignedTeam.toLowerCase().includes(searchTerm.toLowerCase());
    return matchesStatus && matchesSeverity && matchesType && matchesSearch;
  });

  // Group incidents by severity for better organization
  const groupedIncidents = {
    critical: filteredIncidents.filter(i => i.severity === 'critical'),
    high: filteredIncidents.filter(i => i.severity === 'high'),
    medium: filteredIncidents.filter(i => i.severity === 'medium'),
    low: filteredIncidents.filter(i => i.severity === 'low')
  };

  const handleResolveWithAI = (incident: Incident) => {
    setSelectedIncident(incident);
    setIsAIModalOpen(true);
  };

  // Handle severity filter clicks from summary cards
  const handleSeverityFilter = (severity: string) => {
    setFilterSeverity(severity === filterSeverity ? 'all' : severity);
    setOpenAccordion(''); // Close any open accordion when filtering
  };

  // Handle accordion value changes and auto-scroll to the opened accordion
  const handleAccordionChange = (value: string) => {
    setOpenAccordion(value);
    
    // If an accordion was opened, scroll it to the top/center
    if (value && incidentsScrollRef.current) {
      setTimeout(() => {
        const accordionElement = accordionRefs.current[value];
        if (accordionElement) {
          const scrollContainer = incidentsScrollRef.current;
          if (scrollContainer) {
            const containerRect = scrollContainer.getBoundingClientRect();
            const elementRect = accordionElement.getBoundingClientRect();
            
            // Calculate the scroll position to center the accordion
            const scrollTop = scrollContainer.scrollTop + elementRect.top - containerRect.top - 20;
            
            scrollContainer.scrollTo({
              top: Math.max(0, scrollTop),
              behavior: 'smooth'
            });
          }
        }
      }, 150); // Slight delay to ensure accordion expansion animation completes
    }
  };

  const renderIncidentAccordion = (incidents: Incident[], severityLevel: string) => {
    if (incidents.length === 0) return null;

    return (
      <Card 
        className={`mb-6 border-l-4 shadow-lg hover:shadow-xl transition-all duration-300 ${getSeverityColor(severityLevel, incidents[0].type)}`}
        ref={(el) => {
          if (el) accordionRefs.current[severityLevel] = el;
        }}
      >
        <CardHeader className="pb-2">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              {getSeverityIcon(severityLevel)}
              <CardTitle className="text-lg font-semibold capitalize">
                {severityLevel} ({incidents.length})
              </CardTitle>
              {/* Safety Alert Indicator for Critical Safety Incidents */}
              {severityLevel === 'critical' && incidents.some(i => i.type === 'safety') && (
                <div className="flex items-center gap-1 px-2 py-1 bg-red-600 text-white rounded text-xs font-medium animate-pulse">
                  <Shield className="h-3 w-3" />
                  SAFETY ALERT
                </div>
              )}
            </div>
            <Badge 
              variant={getSeverityBadgeVariant(severityLevel)} 
              className="text-xs"
            >
              {severityLevel}
            </Badge>
          </div>
        </CardHeader>
        <CardContent className="pt-0">
          <Accordion 
            type="single" 
            collapsible
            className="w-full space-y-2"
            value={openAccordion}
            onValueChange={handleAccordionChange}
          >
            {incidents.map((incident) => (
              <AccordionItem 
                key={incident.id} 
                value={incident.id} 
                className={`border border-border/60 rounded-lg shadow-sm hover:shadow-md transition-all duration-200 overflow-hidden ${
                  incident.type === 'safety' 
                    ? 'bg-gradient-to-r from-red-50 to-red-100/50 dark:from-red-950/30 dark:to-red-900/20 border-red-200 dark:border-red-800' 
                    : 'bg-gradient-to-r from-background to-muted/20'
                }`}
                ref={(el) => {
                  if (el) accordionRefs.current[incident.id] = el;
                }}
              >
                <AccordionTrigger className="hover:no-underline px-4 py-3 hover:bg-muted/50 transition-colors duration-200">
                  <div className="flex items-center justify-between w-full pr-2">
                    <div className="flex items-center gap-3">
                      <Badge variant="outline" className="font-mono text-xs px-2 py-1">
                        {incident.id}
                      </Badge>
                      <span className="font-medium text-left">{incident.title}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Badge variant={getTypeBadgeVariant(incident.type || 'process')} className="text-xs flex items-center gap-1">
                        {getTypeIcon(incident.type || 'process')}
                        {incident.type || 'process'}
                      </Badge>
                      <Badge variant={getStatusBadgeVariant(incident.status)} className="text-xs">
                        {incident.status.replace('-', ' ')}
                      </Badge>
                    </div>
                  </div>
                </AccordionTrigger>
                <AccordionContent className="px-4 pb-4 pt-1">
                  <div className="space-y-3">
                    {/* Compact Info Row */}
                    <div className="flex items-center gap-4 text-xs text-muted-foreground bg-muted/30 p-2 rounded">
                      <span>📅 {format(parseISO(incident.reportedAt), 'MMM dd, HH:mm')}</span>
                      <span>👥 {incident.assignedTeam}</span>
                      <span className={getTypeColor(incident.type || 'process')}>
                        {getTypeIcon(incident.type || 'process')} {incident.type || 'process'}
                      </span>
                      {incident.resolvedAt && (
                        <span className="text-green-600">✅ Resolved {format(parseISO(incident.resolvedAt), 'MMM dd')}</span>
                      )}
                    </div>

                    {/* Description */}
                    <div>
                      <p className="text-sm text-muted-foreground leading-relaxed">
                        {incident.description}
                      </p>
                    </div>

                    {/* Resolution Notes - Only if exists */}
                    {incident.resolutionNotes && (
                      <div className="bg-green-50 dark:bg-green-950/20 p-2 rounded border border-green-200 dark:border-green-800">
                        <h5 className="text-xs font-medium text-green-700 dark:text-green-300 mb-1">Resolution:</h5>
                        <p className="text-xs text-green-600 dark:text-green-400">{incident.resolutionNotes}</p>
                      </div>
                    )}

                    {/* Latest Update - Only most recent */}
                    {incident.updates && incident.updates.length > 0 && (
                      <div className="bg-blue-50 dark:bg-blue-950/20 p-2 rounded border border-blue-200 dark:border-blue-800">
                        <h5 className="text-xs font-medium text-blue-700 dark:text-blue-300 mb-1">Latest Update:</h5>
                        <p className="text-xs text-blue-600 dark:text-blue-400">
                          <strong>{incident.updates[incident.updates.length - 1].author}</strong> - {incident.updates[incident.updates.length - 1].note}
                        </p>
                      </div>
                    )}

                    {/* Action Button */}
                    <div className="pt-2">
                      <Button 
                        onClick={() => handleResolveWithAI(incident)}
                        size="sm"
                        className="flex items-center gap-2"
                        disabled={incident.status === 'resolved'}
                      >
                        <Bot className="h-4 w-4" />
                        Resolve with AI
                      </Button>
                    </div>
                  </div>
                </AccordionContent>
              </AccordionItem>
            ))}
          </Accordion>
        </CardContent>
      </Card>
    );
  };

  return (
    <div className="h-screen flex flex-col">
      {/* Filters and Search */}
      <div className="flex-shrink-0 space-y-4 p-4 border-b bg-background">
        <Card>
          <CardHeader className="pb-3">
            <CardTitle>Filters & Search</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex flex-wrap items-center gap-4">
              <Input 
                placeholder="Search incidents, teams, descriptions..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="max-w-sm"
              />
              <Select value={filterStatus} onValueChange={setFilterStatus}>
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="Filter by Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Statuses</SelectItem>
                  <SelectItem value="open">Open</SelectItem>
                  <SelectItem value="in-progress">In Progress</SelectItem>
                  <SelectItem value="resolved">Resolved</SelectItem>
                </SelectContent>
              </Select>
              <Select value={filterSeverity} onValueChange={setFilterSeverity}>
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="Filter by Severity" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Severities</SelectItem>
                  <SelectItem value="critical">Critical</SelectItem>
                  <SelectItem value="high">High</SelectItem>
                  <SelectItem value="medium">Medium</SelectItem>
                  <SelectItem value="low">Low</SelectItem>
                </SelectContent>
              </Select>
              <Select value={filterType} onValueChange={setFilterType}>
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="Filter by Type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Types</SelectItem>
                  <SelectItem value="safety">Safety</SelectItem>
                  <SelectItem value="process">Process</SelectItem>
                  <SelectItem value="maintenance">Maintenance</SelectItem>
                  <SelectItem value="quality">Quality</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </CardContent>
        </Card>

        {/* Interactive Summary Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card 
            className={`cursor-pointer transition-all duration-200 hover:shadow-md hover:scale-105 ${
              filterSeverity === 'critical' ? 'ring-2 ring-red-500 bg-red-50 dark:bg-red-950/20' : 'hover:bg-red-50 dark:hover:bg-red-950/10'
            }`}
            onClick={() => handleSeverityFilter('critical')}
          >
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <AlertTriangle className="h-5 w-5 text-red-500" />
                <div>
                  <p className="text-2xl font-bold">{groupedIncidents.critical.length}</p>
                  <p className="text-sm text-muted-foreground">Critical</p>
                </div>
              </div>
              {filterSeverity === 'critical' && (
                <div className="mt-2 text-xs text-red-600 dark:text-red-400 font-medium">
                  ✓ Filtered
                </div>
              )}
            </CardContent>
          </Card>
          
          <Card 
            className={`cursor-pointer transition-all duration-200 hover:shadow-md hover:scale-105 ${
              filterSeverity === 'high' ? 'ring-2 ring-orange-500 bg-orange-50 dark:bg-orange-950/20' : 'hover:bg-orange-50 dark:hover:bg-orange-950/10'
            }`}
            onClick={() => handleSeverityFilter('high')}
          >
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <AlertCircle className="h-5 w-5 text-orange-500" />
                <div>
                  <p className="text-2xl font-bold">{groupedIncidents.high.length}</p>
                  <p className="text-sm text-muted-foreground">High</p>
                </div>
              </div>
              {filterSeverity === 'high' && (
                <div className="mt-2 text-xs text-orange-600 dark:text-orange-400 font-medium">
                  ✓ Filtered
                </div>
              )}
            </CardContent>
          </Card>
          
          <Card 
            className={`cursor-pointer transition-all duration-200 hover:shadow-md hover:scale-105 ${
              filterSeverity === 'medium' ? 'ring-2 ring-blue-500 bg-blue-50 dark:bg-blue-950/20' : 'hover:bg-blue-50 dark:hover:bg-blue-950/10'
            }`}
            onClick={() => handleSeverityFilter('medium')}
          >
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <Info className="h-5 w-5 text-blue-500" />
                <div>
                  <p className="text-2xl font-bold">{groupedIncidents.medium.length}</p>
                  <p className="text-sm text-muted-foreground">Medium</p>
                </div>
              </div>
              {filterSeverity === 'medium' && (
                <div className="mt-2 text-xs text-blue-600 dark:text-blue-400 font-medium">
                  ✓ Filtered
                </div>
              )}
            </CardContent>
          </Card>
          
          <Card 
            className={`cursor-pointer transition-all duration-200 hover:shadow-md hover:scale-105 ${
              filterSeverity === 'low' ? 'ring-2 ring-green-500 bg-green-50 dark:bg-green-950/20' : 'hover:bg-green-50 dark:hover:bg-green-950/10'
            }`}
            onClick={() => handleSeverityFilter('low')}
          >
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <CheckCircle className="h-5 w-5 text-green-500" />
                <div>
                  <p className="text-2xl font-bold">{groupedIncidents.low.length}</p>
                  <p className="text-sm text-muted-foreground">Low</p>
                </div>
              </div>
              {filterSeverity === 'low' && (
                <div className="mt-2 text-xs text-green-600 dark:text-green-400 font-medium">
                  ✓ Filtered
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Scrollable Incidents Container */}
      <div className="flex-1 min-h-0">
        <ScrollArea className="h-full">
          <div ref={incidentsScrollRef} className="p-6 space-y-4">
            {renderIncidentAccordion(groupedIncidents.critical, 'critical')}
            {renderIncidentAccordion(groupedIncidents.high, 'high')}
            {renderIncidentAccordion(groupedIncidents.medium, 'medium')}
            {renderIncidentAccordion(groupedIncidents.low, 'low')}
            
            {filteredIncidents.length === 0 && (
              <Card>
                <CardContent className="p-8 text-center">
                  <AlertTriangle className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
                  <p className="text-lg font-medium mb-2">No incidents found</p>
                  <p className="text-muted-foreground">
                    {searchTerm || filterStatus !== 'all' || filterSeverity !== 'all' || filterType !== 'all'
                      ? 'Try adjusting your filters or search terms.'
                      : 'All systems are operating normally.'}
                  </p>
                </CardContent>
              </Card>
            )}
          </div>
        </ScrollArea>
      </div>

      {/* AI Resolution Modal */}
      {selectedIncident && (
        <AIResolutionModal 
          incident={selectedIncident}
          isOpen={isAIModalOpen}
          onClose={() => {
            setIsAIModalOpen(false);
            setSelectedIncident(null);
          }}
        />
      )}
    </div>
  );
};

export default IncidentManagement; 