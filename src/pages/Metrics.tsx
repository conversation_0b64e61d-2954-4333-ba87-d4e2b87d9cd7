import { useState, useEffect } from "react";
import { Gauge } from "lucide-react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { MetricsOverview } from "@/components/metrics/MetricsOverview";
import { ProductionChart } from "@/components/metrics/ProductionChart";
import { DowntimeChart } from "@/components/metrics/DowntimeChart";
import { ProductivityChart } from "@/components/metrics/ProductivityChart";

const Metrics = () => {
  const [timeFrame, setTimeFrame] = useState<string>("day");

  // Simulate data loading
  useEffect(() => {
    const timer = setTimeout(() => {
      console.log("Metrics data loaded for", timeFrame);
    }, 500);
    
    return () => clearTimeout(timer);
  }, []);

  return (
    <div className="container mx-auto px-4 py-4">
      {/* Time Period Selector */}
      <div>
        <Tabs value={timeFrame} onValueChange={setTimeFrame} className="w-[400px]">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-lg font-medium">Manufacturing Performance</h2>
            <TabsList>
              <TabsTrigger value="day">Day</TabsTrigger>
              <TabsTrigger value="week">Week</TabsTrigger>
              <TabsTrigger value="month">Month</TabsTrigger>
              <TabsTrigger value="quarter">Quarter</TabsTrigger>
              <TabsTrigger value="year">Year</TabsTrigger>
            </TabsList>
          </div>
        </Tabs>
      </div>
      
      {/* Metrics Overview */}
      <div className="mt-4">
        <MetricsOverview />
      </div>
      
      {/* Information Alert */}
      <Alert className="mt-6 bg-blue-50 border-blue-200">
        <AlertDescription className="text-blue-700">
          This dashboard shows manufacturing KPIs based on data collected from our factory equipment monitoring system. Data is collected in real-time from all connected devices.
        </AlertDescription>
      </Alert>
      
      {/* Charts */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
        <ProductionChart />
        <DowntimeChart />
      </div>
      
      {/* Yearly Productivity Chart */}
      <div className="mt-6">
        <ProductivityChart />
      </div>
    </div>
  );
}

export default Metrics;
