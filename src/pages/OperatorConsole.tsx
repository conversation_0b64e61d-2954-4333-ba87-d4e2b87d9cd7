import React, { useState, useEffect, useRef } from "react";
import { 
  MessageSquare, 
  Send, 
  User, 
  Bot, 
  Search,
  BookOpen,
  AlertCircle
} from "lucide-react";

interface ChatMessage {
  id: string;
  content: string;
  sender: "user" | "assistant";
  timestamp: Date;
}

interface KnowledgeItem {
  id: string;
  title: string;
  category: string;
  description: string;
  lastAccessed: string;
  priority: "high" | "medium" | "low";
}

interface SystemAlert {
  id: string;
  message: string;
  severity: "info" | "warning" | "error";
  timestamp: string;
  component: string;
}

const OperatorConsole: React.FC = () => {
  const [messages, setMessages] = useState<ChatMessage[]>([
    {
      id: "1",
      content: "Hello! I'm your Industrial AI Assistant. I can help you with equipment status, production metrics, safety procedures, troubleshooting guides, and operational insights. What would you like to know?",
      sender: "assistant",
      timestamp: new Date()
    }
  ]);
  const [inputMessage, setInputMessage] = useState("");
  const [isTyping, setIsTyping] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const chatEndRef = useRef<HTMLDivElement>(null);

  // Mock knowledge base items
  const knowledgeItems: KnowledgeItem[] = [
    {
      id: "1",
      title: "Emergency Shutdown Procedure",
      category: "Safety",
      description: "Step-by-step emergency shutdown protocol for all critical systems",
      lastAccessed: "2 hours ago",
      priority: "high"
    },
    {
      id: "2",
      title: "Conveyor Belt Maintenance",
      category: "Maintenance",
      description: "Routine maintenance procedures for conveyor belt systems",
      lastAccessed: "1 day ago",
      priority: "medium"
    },
    {
      id: "3",
      title: "Temperature Sensor Calibration",
      category: "Calibration",
      description: "Calibration procedures for temperature monitoring sensors",
      lastAccessed: "3 days ago",
      priority: "medium"
    },
    {
      id: "4",
      title: "Quality Control Standards",
      category: "Quality",
      description: "Current quality control standards and testing procedures",
      lastAccessed: "5 hours ago",
      priority: "high"
    },
    {
      id: "5",
      title: "Hydraulic System Troubleshooting",
      category: "Maintenance",
      description: "Common hydraulic system issues and resolution steps",
      lastAccessed: "1 week ago",
      priority: "medium"
    },
    {
      id: "6",
      title: "Safety Data Sheets Archive",
      category: "Safety",
      description: "Chemical safety data sheets and handling procedures",
      lastAccessed: "3 days ago",
      priority: "high"
    },
    {
      id: "7",
      title: "Equipment Operating Manuals",
      category: "Operations",
      description: "Complete operating manuals for all factory equipment",
      lastAccessed: "2 days ago",
      priority: "medium"
    },
    {
      id: "8",
      title: "Preventive Maintenance Schedule",
      category: "Maintenance",
      description: "Scheduled maintenance tasks and intervals for all equipment",
      lastAccessed: "6 hours ago",
      priority: "high"
    }
  ];

  // Mock system alerts
  const systemAlerts: SystemAlert[] = [
    {
      id: "1",
      message: "Scheduled maintenance window starting in 30 minutes",
      severity: "info",
      timestamp: "2 min ago",
      component: "Line 3 Conveyor"
    },
    {
      id: "2",
      message: "Temperature sensor reading above normal range",
      severity: "warning", 
      timestamp: "5 min ago",
      component: "Furnace Unit 2"
    },
    {
      id: "3",
      message: "Production target exceeded by 12%",
      severity: "info",
      timestamp: "15 min ago",
      component: "Production Line A"
    },
    {
      id: "4",
      message: "Low hydraulic pressure detected",
      severity: "warning",
      timestamp: "8 min ago",
      component: "Press Machine 1"
    },
    {
      id: "5",
      message: "Backup power system test completed successfully",
      severity: "info",
      timestamp: "1 hour ago",
      component: "Emergency Systems"
    },
    {
      id: "6",
      message: "Air filtration system requires filter replacement",
      severity: "warning",
      timestamp: "2 hours ago",
      component: "HVAC System"
    }
  ];

  const handleSendMessage = async () => {
    if (!inputMessage.trim()) return;

    const userMessage: ChatMessage = {
      id: Date.now().toString(),
      content: inputMessage,
      sender: "user",
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    setInputMessage("");
    setIsTyping(true);

    // Simulate AI response with contextual answers
    setTimeout(() => {
      let aiResponse = "";
      const query = inputMessage.toLowerCase();

      if (query.includes("temperature") || query.includes("temp")) {
        aiResponse = "I can help you with temperature-related procedures. Check the 'Temperature Sensor Calibration' guide in the knowledge base. There's currently a warning about temperature sensors on Furnace Unit 2 reading above normal range.";
      } else if (query.includes("maintenance") || query.includes("service")) {
        aiResponse = "For maintenance procedures, refer to the knowledge base. Key documents include 'Conveyor Belt Maintenance', 'Hydraulic System Troubleshooting', and 'Preventive Maintenance Schedule'. There's a scheduled maintenance window starting in 30 minutes for Line 3 Conveyor.";
      } else if (query.includes("alert") || query.includes("warning")) {
        aiResponse = "Current active alerts include: Temperature sensor above normal range (Furnace Unit 2), Low hydraulic pressure (Press Machine 1), and Air filtration system filter replacement needed (HVAC System). The scheduled maintenance for Line 3 Conveyor starts in 30 minutes.";
      } else if (query.includes("emergency") || query.includes("shutdown")) {
        aiResponse = "For emergency procedures, please refer to the 'Emergency Shutdown Procedure' in the knowledge base. This is a high-priority document that contains step-by-step protocols for all critical systems. In case of immediate danger, follow the posted emergency procedures.";
      } else if (query.includes("safety")) {
        aiResponse = "Safety resources include 'Emergency Shutdown Procedure' and 'Safety Data Sheets Archive' in the knowledge base. Both are high-priority documents. The emergency backup power system test was recently completed successfully.";
      } else if (query.includes("hydraulic")) {
        aiResponse = "For hydraulic system issues, check the 'Hydraulic System Troubleshooting' guide in the knowledge base. There's currently a low hydraulic pressure warning on Press Machine 1 that needs attention.";
      } else {
        aiResponse = "I can help you with safety procedures, maintenance guides, troubleshooting, and operational insights. Try asking about specific equipment, maintenance procedures, safety protocols, or check the knowledge base for detailed documentation.";
      }

      const assistantMessage: ChatMessage = {
        id: (Date.now() + 1).toString(),
        content: aiResponse,
        sender: "assistant",
        timestamp: new Date()
      };

      setMessages(prev => [...prev, assistantMessage]);
      setIsTyping(false);
    }, 1500);
  };

  // Filter knowledge items based on search query
  const filteredKnowledge = knowledgeItems.filter(item =>
    item.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
    item.category.toLowerCase().includes(searchQuery.toLowerCase()) ||
    item.description.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const getSeverityBadge = (severity: string) => {
    switch (severity) {
      case "info": return "bg-blue-100 text-blue-800";
      case "warning": return "bg-yellow-100 text-yellow-800";
      case "error": return "bg-red-100 text-red-800";
      default: return "bg-gray-100 text-gray-800";
    }
  };

  useEffect(() => {
    chatEndRef.current?.scrollIntoView({ behavior: "smooth" });
  }, [messages]);

  return (
    <div className="w-full h-[calc(100vh-140px)] flex bg-gray-50 dark:bg-gray-900 overflow-hidden rounded-lg border border-gray-200 dark:border-gray-700">
      {/* Left Panel - Chat Interface */}
      <div className="w-1/2 border-r border-gray-200 dark:border-gray-700 flex flex-col h-full">
        {/* Chat Header - Fixed height */}
        <div className="flex-shrink-0 p-3 border-b border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800">
          <div className="flex items-center gap-3">
            <MessageSquare className="h-5 w-5 text-blue-600" />
            <div>
              <h2 className="text-base font-semibold text-gray-800 dark:text-gray-200">Industrial AI Assistant</h2>
              <p className="text-xs text-gray-600 dark:text-gray-400">Ask questions about equipment, production, and operations</p>
            </div>
          </div>
        </div>

        {/* Chat Messages - Scrollable */}
        <div className="flex-1 overflow-y-auto p-3 space-y-3 min-h-0">
          {messages.map((message) => (
            <div key={message.id} className={`flex ${message.sender === 'user' ? 'justify-end' : 'justify-start'}`}>
              <div className={`max-w-[80%] rounded-lg p-2 ${
                message.sender === 'user' 
                  ? 'bg-blue-600 text-white' 
                  : 'bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 text-gray-900 dark:text-gray-100'
              }`}>
                <div className="flex items-start gap-2">
                  {message.sender === 'assistant' && <Bot className="h-3 w-3 mt-0.5 text-blue-600" />}
                  {message.sender === 'user' && <User className="h-3 w-3 mt-0.5" />}
                  <div className="flex-1">
                    <p className="text-xs">{message.content}</p>
                    <p className={`text-xs mt-1 opacity-70 ${
                      message.sender === 'user' ? 'text-blue-100' : 'text-gray-500 dark:text-gray-400'
                    }`}>
                      {message.timestamp.toLocaleTimeString()}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          ))}
          
          {isTyping && (
            <div className="flex justify-start">
              <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-2 max-w-[80%]">
                <div className="flex items-center gap-2">
                  <Bot className="h-3 w-3 text-blue-600" />
                  <div className="flex space-x-1">
                    <div className="w-1.5 h-1.5 bg-gray-400 rounded-full animate-bounce"></div>
                    <div className="w-1.5 h-1.5 bg-gray-400 rounded-full animate-bounce" style={{animationDelay: '0.1s'}}></div>
                    <div className="w-1.5 h-1.5 bg-gray-400 rounded-full animate-bounce" style={{animationDelay: '0.2s'}}></div>
                  </div>
                </div>
              </div>
            </div>
          )}
          <div ref={chatEndRef} />
        </div>

        {/* Chat Input - Fixed height */}
        <div className="flex-shrink-0 p-3 border-t border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800">
          <div className="flex items-center gap-2">
            <input
              type="text"
              value={inputMessage}
              onChange={(e) => setInputMessage(e.target.value)}
              onKeyPress={(e) => e.key === 'Enter' && handleSendMessage()}
              placeholder="Ask about equipment status, production metrics..."
              className="flex-1 px-2 py-1.5 text-xs border border-gray-300 dark:border-gray-600 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100"
            />
            <button
              onClick={handleSendMessage}
              disabled={!inputMessage.trim()}
              className="px-3 py-1.5 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              <Send className="h-3 w-3" />
            </button>
          </div>
        </div>
      </div>

      {/* Right Panel - Knowledge Base & Alerts */}
      <div className="w-1/2 h-full flex flex-col bg-white dark:bg-gray-800">
        {/* Dashboard Header - Fixed */}
        <div className="flex-shrink-0 p-3 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <span className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></span>
              <span className="text-xs font-medium text-gray-600 dark:text-gray-400">Knowledge Base & Alerts</span>
            </div>
            <span className="text-xs text-gray-500 dark:text-gray-400">
              {new Date().toLocaleTimeString()}
            </span>
          </div>
        </div>

        {/* Dashboard Content - Scrollable */}
        <div className="flex-1 overflow-y-auto p-3 space-y-6 min-h-0">
          {/* Knowledge Base */}
          <div className="flex-1">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-sm font-semibold text-gray-800 dark:text-gray-200 flex items-center gap-2">
                <BookOpen className="h-4 w-4 text-blue-600" />
                Knowledge Base
              </h3>
              <div className="flex items-center gap-2">
                <Search className="h-3 w-3 text-gray-400" />
                <input
                  type="text"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  placeholder="Search procedures..."
                  className="px-2 py-1 text-xs border border-gray-300 dark:border-gray-600 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100 w-32"
                />
              </div>
            </div>
            <div className="space-y-2 max-h-72 overflow-y-auto">
              {filteredKnowledge.map((item) => (
                <div key={item.id} className="bg-gray-50 dark:bg-gray-700 rounded-lg p-3 border border-gray-200 dark:border-gray-600 hover:shadow-sm transition-shadow cursor-pointer">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-1">
                        <h4 className="font-medium text-gray-900 dark:text-gray-100 text-sm">{item.title}</h4>
                        <span className={`text-xs px-2 py-0.5 rounded-full ${
                          item.priority === "high" ? "bg-red-100 text-red-800" :
                          item.priority === "medium" ? "bg-yellow-100 text-yellow-800" :
                          "bg-green-100 text-green-800"
                        }`}>
                          {item.priority}
                        </span>
                      </div>
                      <p className="text-xs text-gray-600 dark:text-gray-400 mb-2">{item.description}</p>
                      <div className="flex items-center justify-between">
                        <span className="text-xs text-blue-600 font-medium">{item.category}</span>
                        <span className="text-xs text-gray-500 dark:text-gray-400">Last accessed: {item.lastAccessed}</span>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* System Alerts */}
          <div className="flex-1">
            <h3 className="text-sm font-semibold text-gray-800 dark:text-gray-200 mb-4 flex items-center gap-2">
              <AlertCircle className="h-4 w-4 text-orange-600" />
              System Alerts
            </h3>
            <div className="space-y-2 max-h-64 overflow-y-auto">
              {systemAlerts.map((alert) => (
                <div key={alert.id} className="bg-gray-50 dark:bg-gray-700 rounded-lg p-3 border border-gray-200 dark:border-gray-600">
                  <div className="flex items-start gap-3">
                    <div className={`px-2 py-0.5 rounded-full text-xs font-medium ${getSeverityBadge(alert.severity)}`}>
                      {alert.severity.toUpperCase()}
                    </div>
                    <div className="flex-1">
                      <p className="text-sm text-gray-900 dark:text-gray-100 font-medium">{alert.message}</p>
                      <div className="flex items-center justify-between mt-1">
                        <span className="text-xs text-gray-600 dark:text-gray-400">{alert.component}</span>
                        <span className="text-xs text-gray-500 dark:text-gray-400">{alert.timestamp}</span>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default OperatorConsole; 