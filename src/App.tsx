import { Toaster } from "@/components/ui/toaster";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter as Router, Routes, Route } from "react-router-dom";
import AppLayout from "./components/AppLayout";
import Index from "./pages/Index";
import CameraStreams from "./pages/CameraStreams";
import SensorGraphs from "./pages/SensorGraphs";
import Metrics from "./pages/Metrics";
import IncidentManagement from "./pages/IncidentManagement";
import OperatorConsole from "./pages/OperatorConsole";
import NotFound from "./pages/NotFound";
import PageTransition from "./components/PageTransition";

// Add Google Fonts for Inter
const fontLink = document.createElement('link');
fontLink.href = 'https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap';
fontLink.rel = 'stylesheet';
document.head.appendChild(fontLink);

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 1,
      refetchOnWindowFocus: false,
    },
  },
});

const AppRoutes = () => {
  return (
    <Routes>
      <Route path="/" element={
        <PageTransition>
          <AppLayout><Index /></AppLayout>
        </PageTransition>
      } />
      <Route path="/cameras" element={
        <PageTransition>
          <AppLayout><CameraStreams /></AppLayout>
        </PageTransition>
      } />
      <Route path="/sensors" element={
        <PageTransition>
          <AppLayout><SensorGraphs /></AppLayout>
        </PageTransition>
      } />
      <Route path="/metrics" element={
        <PageTransition>
          <AppLayout><Metrics /></AppLayout>
        </PageTransition>
      } />
      <Route path="/incidents" element={
        <PageTransition>
          <AppLayout><IncidentManagement /></AppLayout>
        </PageTransition>
      } />
      <Route path="/console" element={
        <PageTransition>
          <AppLayout><OperatorConsole /></AppLayout>
        </PageTransition>
      } />
      {/* ADD ALL CUSTOM ROUTES ABOVE THE CATCH-ALL "*" ROUTE */}
      <Route path="*" element={<NotFound />} />
    </Routes>
  );
};

const App = () => (
  <QueryClientProvider client={queryClient}>
    <TooltipProvider>
      <Toaster />
      <Router>
        <AppRoutes />
      </Router>
    </TooltipProvider>
  </QueryClientProvider>
);

export default App;
