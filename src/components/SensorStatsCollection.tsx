
import React, { useState } from 'react';
import SensorStats from './SensorStats';
import { Button } from "@/components/ui/button";
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible";
import { ChevronDown, ChevronUp } from "lucide-react";

interface SensorStatsCollectionProps {
  sensorTypes: Array<{
    id: string;
    name: string;
    unit: string;
    icon: string;
    description: string;
    color: string;
    data: any[];
  }>;
  zones: Array<{
    id: string;
    name: string;
    color: string;
  }>;
  selectedZones: string[];
}

export function SensorStatsCollection({ 
  sensorTypes, 
  zones, 
  selectedZones 
}: SensorStatsCollectionProps) {
  const [isOpen, setIsOpen] = useState(false);
  
  // Create sensor stats for each sensor type and selected zone
  const getLatestReadings = () => {
    const sensors: any[] = [];
    
    sensorTypes.forEach(sensorType => {
      selectedZones.forEach(zoneId => {
        const zone = zones.find(z => z.id === zoneId);
        if (!zone) return;
        
        // Get the latest data point (last item in the data array)
        const latestData = sensorType.data[sensorType.data.length - 1];
        if (!latestData || latestData[zoneId] === undefined) return;
        
        // Determine min, max, avg for this sensor in this zone
        const allValues = sensorType.data.map(d => d[zoneId]).filter(val => val !== undefined);
        const min = Math.min(...allValues);
        const max = Math.max(...allValues);
        const avg = allValues.reduce((sum, val) => sum + val, 0) / allValues.length;
        
        // Calculate change percentage from previous reading
        const previousData = sensorType.data[sensorType.data.length - 2];
        let changePercentage;
        if (previousData && previousData[zoneId] !== undefined && previousData[zoneId] !== 0) {
          changePercentage = ((latestData[zoneId] - previousData[zoneId]) / previousData[zoneId]) * 100;
        }
        
        // Determine status based on the current value relative to min/max range
        let status: 'normal' | 'warning' | 'alert' | 'offline' = 'normal';
        const range = max - min;
        const normalizedValue = (latestData[zoneId] - min) / range;
        if (normalizedValue > 0.85) status = 'warning';
        if (normalizedValue > 0.95) status = 'alert';
        
        sensors.push({
          id: `${sensorType.id}-${zoneId}`,
          name: `${sensorType.name} - ${zone.name}`,
          type: sensorType.id,
          zone: zoneId,
          currentValue: latestData[zoneId],
          unit: sensorType.unit,
          status,
          changePercentage,
          min: parseFloat(min.toFixed(2)),
          max: parseFloat(max.toFixed(2)),
          avg: parseFloat(avg.toFixed(2))
        });
      });
    });
    
    return sensors;
  };
  
  const sensorReadings = getLatestReadings();
  
  // Divide the sensors into initial view (first 6 - two rows on desktop) and the rest
  const initialSensors = sensorReadings.slice(0, 6);
  const remainingSensors = sensorReadings.slice(6);
  
  const hasMoreSensors = remainingSensors.length > 0;

  return (
    <div className="space-y-4">
      {/* Initial two rows of sensors */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {initialSensors.map(sensor => (
          <SensorStats key={sensor.id} sensor={sensor} />
        ))}
      </div>
      
      {/* Collapsible section for remaining sensors */}
      {hasMoreSensors && (
        <Collapsible
          open={isOpen}
          onOpenChange={setIsOpen}
          className="border-t pt-2 mt-4"
        >
          <div className="flex justify-center">
            <CollapsibleTrigger asChild>
              <Button variant="ghost" size="sm" className="flex items-center gap-1">
                {isOpen ? (
                  <>
                    <ChevronUp className="h-4 w-4" />
                    <span>Show Less</span>
                  </>
                ) : (
                  <>
                    <ChevronDown className="h-4 w-4" />
                    <span>Show More ({remainingSensors.length} additional readings)</span>
                  </>
                )}
              </Button>
            </CollapsibleTrigger>
          </div>
          
          <CollapsibleContent className="mt-4">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {remainingSensors.map(sensor => (
                <SensorStats key={sensor.id} sensor={sensor} />
              ))}
            </div>
          </CollapsibleContent>
        </Collapsible>
      )}
      
      {/* Display message when no sensors are available */}
      {sensorReadings.length === 0 && (
        <div className="col-span-full text-center py-8 text-muted-foreground">
          No sensor data available for the selected zones.
        </div>
      )}
    </div>
  );
}
