import { useState, useEffect, useRef } from "react";
import { Factory, ZoomIn, ZoomOut, Move } from "lucide-react";
import TenantSelector from "./TenantSelector";
import FactoryFloorPlan from "./FactoryFloorPlan";
import StatusPanel from "./StatusPanel";
import MetricsSummary from "./MetricsSummary";
import { Button } from "@/components/ui/button";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";

const FactoryDashboard = () => {
  // State for time
  const [currentDateTime, setCurrentDateTime] = useState<string>("");
  
  // State for zoom and pan controls
  const [zoomLevel, setZoomLevel] = useState(100);
  const floorPlanRef = useRef<HTMLDivElement>(null);

  // Update time every minute
  useEffect(() => {
    const updateTime = () => {
      try {
        const now = new Date();
        const timeString = now.toLocaleTimeString() + " UTC • Live";
        setCurrentDateTime(timeString);
      } catch (error) {
        console.error("Error updating time:", error);
        setCurrentDateTime("Time unavailable");
      }
    };
    
    updateTime();
    const interval = setInterval(updateTime, 60000);
    return () => clearInterval(interval);
  }, []);

  // Zoom in function
  const handleZoomIn = () => {
    if (zoomLevel < 150) {
      setZoomLevel(prev => prev + 10);
      if (floorPlanRef.current) {
        floorPlanRef.current.style.transform = `scale(${(zoomLevel + 10) / 100})`;
      }
    }
  };

  // Zoom out function
  const handleZoomOut = () => {
    if (zoomLevel > 70) {
      setZoomLevel(prev => prev - 10);
      if (floorPlanRef.current) {
        floorPlanRef.current.style.transform = `scale(${(zoomLevel - 10) / 100})`;
      }
    }
  };

  const displayTime = currentDateTime || "Loading time...";

  return (
    <div className="container mx-auto px-3 py-3">
      {/* Time and Tenant Selector */}
      <div className="flex justify-end items-center gap-4 px-3 py-2 mb-4 border-b border-border">
        <div className="flex items-center gap-2">
          <span className="w-2 h-2 bg-green-500 rounded-full"></span>
          <span className="text-sm text-muted-foreground">{displayTime}</span>
        </div>
        <TenantSelector />
      </div>
      
      {/* KPI Summary */}
      <div className="mb-4">
        <MetricsSummary />
      </div>
      
      <div className="flex flex-col lg:flex-row gap-4">
        <div className="lg:w-3/4">
          <div className="bg-card rounded-lg shadow-sm p-3 mb-4">
            <div className="flex items-center justify-between mb-3">
              <div className="flex items-center gap-2">
                <Factory className="h-5 w-5 text-primary" />
                <h2 className="text-xl font-semibold text-foreground">Factory Floor Plan</h2>
              </div>
              
              {/* Zoom controls */}
              <div className="flex items-center gap-2">
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button 
                        variant="outline" 
                        size="icon" 
                        onClick={handleZoomOut}
                        disabled={zoomLevel <= 70}
                        className="h-8 w-8"
                      >
                        <ZoomOut className="h-4 w-4" />
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>Zoom Out</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
                
                <span className="text-xs text-muted-foreground">{zoomLevel}%</span>
                
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button 
                        variant="outline" 
                        size="icon" 
                        onClick={handleZoomIn}
                        disabled={zoomLevel >= 150}
                        className="h-8 w-8"
                      >
                        <ZoomIn className="h-4 w-4" />
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>Zoom In</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
                
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button 
                        variant="outline" 
                        size="icon"
                        className="h-8 w-8"
                        onClick={() => {
                          setZoomLevel(100);
                          if (floorPlanRef.current) {
                            floorPlanRef.current.style.transform = 'scale(1)';
                          }
                        }}
                      >
                        <Move className="h-4 w-4" />
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>Reset View</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </div>
            </div>
            
            <div className="h-[580px] bg-muted/20 rounded-lg border border-border overflow-hidden transition-all duration-300 ease-in-out">
              <div 
                ref={floorPlanRef} 
                className="w-full h-full origin-center transition-transform duration-300 ease-in-out"
                style={{ transform: `scale(${zoomLevel / 100})` }}
              >
                <FactoryFloorPlan />
              </div>
            </div>
          </div>
        </div>
        
        <div className="lg:w-1/4">
          <StatusPanel />
        </div>
      </div>
    </div>
  );
};

export default FactoryDashboard;
