
import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { ArrowUp, ArrowDown, ThermometerIcon, Wind, Droplets, Wifi, Zap } from "lucide-react";
import { cn } from "@/lib/utils";

interface SensorStatsProps {
  sensor: {
    id: string;
    name: string;
    type: string;
    zone: string;
    currentValue: number;
    unit: string;
    status: 'normal' | 'warning' | 'alert' | 'offline';
    changePercentage?: number;
    min?: number;
    max?: number;
    avg?: number;
  };
}

export default function SensorStats({ sensor }: SensorStatsProps) {
  // Determine the icon based on sensor type
  const getIcon = () => {
    switch (sensor.type) {
      case 'temperature':
        return <ThermometerIcon className="h-5 w-5" />;
      case 'humidity':
        return <Droplets className="h-5 w-5" />;
      case 'pressure':
        return <Wind className="h-5 w-5" />;
      case 'power':
        return <Zap className="h-5 w-5" />;
      default:
        return <Wifi className="h-5 w-5" />;
    }
  };
  
  // Determine status color
  const getStatusColor = () => {
    switch (sensor.status) {
      case 'normal':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'warning':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'alert':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'offline':
        return 'bg-gray-100 text-gray-800 border-gray-200';
      default:
        return 'bg-blue-100 text-blue-800 border-blue-200';
    }
  };
  
  // Format change percentage with arrow icon
  const formatChange = () => {
    if (sensor.changePercentage === undefined) return null;
    
    const isPositive = sensor.changePercentage > 0;
    const changeClass = isPositive ? 'text-green-600' : 'text-red-600';
    
    return (
      <div className={cn("flex items-center gap-1 text-sm", changeClass)}>
        {isPositive ? 
          <ArrowUp className="h-3.5 w-3.5" /> : 
          <ArrowDown className="h-3.5 w-3.5" />
        }
        <span>{Math.abs(sensor.changePercentage).toFixed(1)}%</span>
      </div>
    );
  };
  
  // For displaying units on sensor readings
  const formatValue = (value?: number) => {
    if (value === undefined) return 'N/A';
    return `${value}${sensor.unit}`;
  };
  
  return (
    <Card>
      <CardHeader className="pb-2">
        <div className="flex justify-between items-start">
          <div className="flex items-center gap-2">
            {getIcon()}
            <CardTitle className="text-lg">{sensor.name}</CardTitle>
          </div>
          <Badge className={getStatusColor()}>
            {sensor.status.charAt(0).toUpperCase() + sensor.status.slice(1)}
          </Badge>
        </div>
      </CardHeader>
      <CardContent>
        <div className="flex justify-between items-baseline">
          <div className="flex items-baseline gap-1">
            <span className="text-3xl font-bold">{sensor.currentValue}</span>
            <span className="text-sm text-muted-foreground">{sensor.unit}</span>
          </div>
          {formatChange()}
        </div>
        
        <div className="grid grid-cols-3 gap-2 mt-4 text-sm">
          <div className="text-center">
            <div className="text-muted-foreground">Min</div>
            <div>{formatValue(sensor.min)}</div>
          </div>
          <div className="text-center">
            <div className="text-muted-foreground">Avg</div>
            <div>{formatValue(sensor.avg)}</div>
          </div>
          <div className="text-center">
            <div className="text-muted-foreground">Max</div>
            <div>{formatValue(sensor.max)}</div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
