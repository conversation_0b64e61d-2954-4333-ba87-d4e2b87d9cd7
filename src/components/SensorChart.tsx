
import { useEffect, useState } from "react";
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  Area,
  AreaChart,
  TooltipProps
} from "recharts";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { HoverCard, HoverCardTrigger, HoverCardContent } from "@/components/ui/hover-card";
import { Info } from "lucide-react";
import * as LucideIcons from "lucide-react";
import { cn } from "@/lib/utils";

interface SensorChartProps {
  sensorType: {
    id: string;
    name: string;
    unit: string;
    icon: string;
    description: string;
    color: string;
  };
  data: Array<Record<string, any>>;
  zones: Array<{
    id: string;
    name: string;
    color: string;
  }>;
  selectedZones: string[];
}

export function SensorChart({ sensorType, data, zones, selectedZones }: SensorChartProps) {
  const [animate, setAnimate] = useState(false);
  
  // Get the icon component from lucide-react
  const IconComponent = LucideIcons[sensorType.icon as keyof typeof LucideIcons] as React.ComponentType<{ className?: string }>;
  
  useEffect(() => {
    // Trigger animation on mount or when data changes
    setAnimate(true);
    const timer = setTimeout(() => setAnimate(false), 800);
    return () => clearTimeout(timer);
  }, [sensorType, selectedZones]);
  
  const filteredZones = zones.filter((zone) => selectedZones.includes(zone.id));
  
  // Get all valid values for Y axis calculation
  const yValues = data.flatMap(item => 
    selectedZones.map(zone => item[zone])
  ).filter(val => val !== undefined);
  
  // Calculate min and max for Y axis with 5% padding
  const minValue = yValues.length > 0 ? Math.floor(Math.min(...yValues) * 0.95) : 0;
  const maxValue = yValues.length > 0 ? Math.ceil(Math.max(...yValues) * 1.05) : 100;
  
  // Custom tooltip component
  const CustomTooltip = ({ active, payload, label }: TooltipProps<any, any>) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-background border border-border p-3 rounded-lg shadow-md text-sm">
          <p className="font-medium mb-2">{label}</p>
          <div className="space-y-1.5">
            {payload.map((entry, index) => {
              const zone = zones.find(z => z.id === entry.dataKey);
              return (
                <div key={index} className="flex items-center gap-2">
                  <div 
                    className="w-3 h-3 rounded-full" 
                    style={{ backgroundColor: entry.color }} 
                  />
                  <span className="text-muted-foreground">{zone?.name}:</span>
                  <span className="font-medium">{entry.value} {sensorType.unit}</span>
                </div>
              );
            })}
          </div>
        </div>
      );
    }
    return null;
  };
  
  return (
    <Card className={cn(
      "transition-all duration-500 border border-border/50",
      animate ? "scale-[1.01] shadow-md" : "scale-100"
    )}>
      <CardHeader className="pb-2">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            {IconComponent && <IconComponent className="h-5 w-5 text-primary" />}
            <CardTitle className="text-lg">
              {sensorType.name} 
              <span className="text-muted-foreground ml-1 text-sm">
                ({sensorType.unit})
              </span>
            </CardTitle>
          </div>
          <HoverCard>
            <HoverCardTrigger asChild>
              <button className="text-muted-foreground hover:text-foreground transition-colors">
                <Info className="h-4 w-4" />
              </button>
            </HoverCardTrigger>
            <HoverCardContent className="w-80">
              <div className="space-y-2">
                <h4 className="font-medium text-sm">{sensorType.name} Sensors</h4>
                <p className="text-xs text-muted-foreground">{sensorType.description}</p>
                <div className="pt-2">
                  <h5 className="text-xs font-medium mb-1.5">Monitored zones:</h5>
                  <div className="flex flex-wrap gap-1.5">
                    {selectedZones.length === 0 ? (
                      <span className="text-xs text-muted-foreground italic">No zones selected</span>
                    ) : (
                      filteredZones.map(zone => (
                        <div key={zone.id} className="flex items-center text-xs gap-1.5 bg-muted/50 px-2 py-1 rounded-sm">
                          <div className="w-2 h-2 rounded-full" style={{backgroundColor: zone.color}} />
                          <span>{zone.name}</span>
                        </div>
                      ))
                    )}
                  </div>
                </div>
              </div>
            </HoverCardContent>
          </HoverCard>
        </div>
        <CardDescription>
          Readings from the last 24 hours across {selectedZones.length} {selectedZones.length === 1 ? 'zone' : 'zones'}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="h-[350px] mt-4">
          <ResponsiveContainer width="100%" height="100%">
            <AreaChart
              data={data}
              margin={{ top: 10, right: 30, left: 0, bottom: 0 }}
            >
              <defs>
                {filteredZones.map((zone) => (
                  <linearGradient 
                    key={zone.id} 
                    id={`gradient-${zone.id}`} 
                    x1="0" y1="0" x2="0" y2="1"
                  >
                    <stop offset="5%" stopColor={zone.color} stopOpacity={0.4} />
                    <stop offset="95%" stopColor={zone.color} stopOpacity={0.1} />
                  </linearGradient>
                ))}
              </defs>
              <CartesianGrid strokeDasharray="3 3" stroke="#e5e7eb" />
              <XAxis dataKey="time" fontSize={12} tickMargin={8} />
              <YAxis 
                domain={[minValue, maxValue]} 
                fontSize={12}
                tickFormatter={(value) => `${value}`}
              />
              <Tooltip content={<CustomTooltip />} />
              <Legend 
                verticalAlign="bottom" 
                height={36}
                formatter={(value) => {
                  const zone = zones.find(z => z.id === value);
                  return zone ? zone.name : value;
                }}
              />
              {filteredZones.map((zone) => (
                <Area
                  key={zone.id}
                  type="monotone"
                  dataKey={zone.id}
                  stroke={zone.color}
                  strokeWidth={2}
                  fillOpacity={1}
                  fill={`url(#gradient-${zone.id})`}
                  activeDot={{ r: 6, strokeWidth: 1 }}
                  animationDuration={800}
                />
              ))}
            </AreaChart>
          </ResponsiveContainer>
        </div>
      </CardContent>
    </Card>
  );
}
