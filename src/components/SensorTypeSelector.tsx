
import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import * as LucideIcons from "lucide-react";

interface SensorTypeSelectorProps {
  sensorTypes: { 
    id: string; 
    name: string; 
    icon: string;
    color: string;
  }[];
  selectedType: string;
  onTypeChange: (type: string) => void;
}

export function SensorTypeSelector({ 
  sensorTypes, 
  selectedType, 
  onTypeChange 
}: SensorTypeSelectorProps) {
  return (
    <div className="flex overflow-x-auto pb-2 gap-2 no-scrollbar">
      {sensorTypes.map((type) => {
        // Dynamically get the icon component
        const IconComponent = LucideIcons[type.icon as keyof typeof LucideIcons] as React.ComponentType<{ className?: string }>;
        
        return (
          <Button
            key={type.id}
            variant="outline"
            size="sm"
            className={cn(
              "flex-shrink-0 h-9 px-3 border rounded-full transition-all",
              selectedType === type.id 
                ? "bg-primary/10 border-primary/50 shadow-sm" 
                : "bg-background border-border hover:bg-background/80"
            )}
            onClick={() => onTypeChange(type.id)}
          >
            {IconComponent && (
              <IconComponent className={cn(
                "h-4 w-4 mr-2", 
                selectedType === type.id ? "text-primary" : "text-muted-foreground",
                type.color ? `text-[${type.color}]` : ""
              )} />
            )}
            <span className={cn(
              "text-xs font-medium",
              selectedType === type.id ? "text-foreground" : "text-muted-foreground"
            )}>
              {type.name}
            </span>
          </Button>
        );
      })}
    </div>
  );
}
