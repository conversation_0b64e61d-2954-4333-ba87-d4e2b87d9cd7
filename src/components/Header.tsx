import React from 'react';
import { Building2 } from 'lucide-react';

interface HeaderProps {
  title?: string;
  subtitle?: string;
}

const Header: React.FC<HeaderProps> = ({ title, subtitle }) => {
  return (
    <header className="bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-700 mb-4">
      <div className="px-6 py-3">
        <div className="flex items-center gap-4">
          {/* DeepEdge Logo */}
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 bg-gradient-to-br from-blue-600 to-blue-800 rounded-md flex items-center justify-center shadow-sm">
              <Building2 className="h-5 w-5 text-white" />
            </div>
            <div className="flex flex-col">
              <h1 className="text-xl font-bold text-gray-900 dark:text-white leading-tight">
                DeepEdge
              </h1>
              <p className="text-sm text-gray-500 dark:text-gray-400 -mt-0.5 leading-tight">
                Industrial Intelligence Platform
              </p>
            </div>
          </div>
          
          {/* Page Title */}
          {title && (
            <div className="flex items-center gap-3 ml-6">
              <div className="w-px h-10 bg-gray-300 dark:bg-gray-600"></div>
              <div className="flex flex-col">
                <h2 className="text-lg font-semibold text-gray-800 dark:text-gray-200 leading-tight">
                  {title}
                </h2>
                {subtitle && (
                  <p className="text-sm text-gray-600 dark:text-gray-400 -mt-0.5 leading-tight">
                    {subtitle}
                  </p>
                )}
              </div>
            </div>
          )}
        </div>
      </div>
    </header>
  );
};

export default Header; 