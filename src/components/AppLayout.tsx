import { Suspense } from "react";
import { Outlet, useLocation } from "react-router-dom";
import Sidebar from "./Sidebar";
import Header from "./Header";
import LoadingSpinner from "./LoadingSpinner";

const getPageInfo = (pathname: string) => {
  switch (pathname) {
    case '/':
      return { title: 'Factory Dashboard', subtitle: 'Real-time operational monitoring and analytics' };
    case '/cameras':
      return { title: 'Camera Streams', subtitle: 'Live surveillance and monitoring feeds' };
    case '/sensors':
      return { title: 'Sensor Data', subtitle: 'Real-time sensor monitoring and analytics' };
    case '/metrics':
      return { title: 'Performance Metrics', subtitle: 'Key performance indicators and analytics' };
    case '/incidents':
      return { title: 'Incident Management', subtitle: 'AI-powered incident resolution system' };
    case '/console':
      return { title: 'Operator Console', subtitle: 'Central command and control interface' };
    default:
      return { title: undefined, subtitle: undefined };
  }
};

const AppLayout = ({ children }: { children?: React.ReactNode }) => {
  const location = useLocation();
  const { title, subtitle } = getPageInfo(location.pathname);

  return (
    <div className="min-h-screen bg-background flex">
      <Sidebar />
      <div className="flex-1 ml-16 flex flex-col">
        <Header title={title} subtitle={subtitle} />
        <div className="flex-1 p-4 overflow-auto">
          <Suspense fallback={<LoadingSpinner />}>
            {children || <Outlet />}
          </Suspense>
        </div>
      </div>
    </div>
  );
};

export default AppLayout;
