import React, { useState, useRef, useEffect } from 'react';
import { Incident } from '@/utils/configLoader';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Separator } from "@/components/ui/separator";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { format, parseISO } from 'date-fns';
import { 
  Bot, 
  Send, 
  Search, 
  CheckCircle, 
  UserPlus, 
  AlertTriangle, 
  Calendar, 
  User, 
  Clock,
  FileText,
  Settings,
  Zap,
  ExternalLink,
  Copy,
  Download,
  Share,
  ArrowRight,
  X,
  Shield,
  Play,
  Pause,
  Volume2,
  Maximize,
  Image as ImageIcon,
  Video,
  FileDown
} from 'lucide-react';

interface AIResolutionModalProps {
  incident: Incident;
  isOpen: boolean;
  onClose: () => void;
}

interface ChatMessage {
  id: string;
  type: 'user' | 'ai';
  message: string | React.ReactNode;
  timestamp: Date;
}

interface ToastMessage {
  id: string;
  message: string;
  type: 'success' | 'error' | 'info';
}

const AIResolutionModal: React.FC<AIResolutionModalProps> = ({ incident, isOpen, onClose }) => {
  const [chatMessages, setChatMessages] = useState<ChatMessage[]>([]);
  const [inputMessage, setInputMessage] = useState('');
  const [isProcessing, setIsProcessing] = useState(false);
  const [activeTab, setActiveTab] = useState('chat');
  const [jiraAssignee, setJiraAssignee] = useState('');
  const [jiraPriority, setJiraPriority] = useState('Medium');
  const [resolutionNotes, setResolutionNotes] = useState('');
  const [toasts, setToasts] = useState<ToastMessage[]>([]);
  
  // Simple ref for the chat messages container ONLY
  const chatMessagesRef = useRef<HTMLDivElement>(null);

  // Helper functions for incident type handling
  const getIncidentType = () => incident.type || 'process';
  const isSafetyIncident = () => getIncidentType() === 'safety';
  
  // Get type-specific badge variants and colors
  const getTypeBadgeVariant = (type: string): "default" | "secondary" | "destructive" | "outline" => {
    switch (type) {
      case 'safety': return 'destructive';
      case 'process': return 'default';
      case 'maintenance': return 'secondary';
      case 'quality': return 'outline';
      default: return 'outline';
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'safety': return Shield;
      case 'process': return Settings;
      case 'maintenance': return Zap;
      case 'quality': return CheckCircle;
      default: return AlertTriangle;
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'safety': return 'text-red-600';
      case 'process': return 'text-blue-600';
      case 'maintenance': return 'text-orange-600';
      case 'quality': return 'text-green-600';
      default: return 'text-gray-600';
    }
  };

  // Toast functions
  const showToast = (message: string, type: 'success' | 'error' | 'info' = 'success') => {
    const id = Date.now().toString();
    const toast: ToastMessage = { id, message, type };
    setToasts(prev => [...prev, toast]);
    
    // Auto remove after 3 seconds
    setTimeout(() => {
      setToasts(prev => prev.filter(t => t.id !== id));
    }, 3000);
  };

  const removeToast = (id: string) => {
    setToasts(prev => prev.filter(t => t.id !== id));
  };

  // Simple scroll function - ONLY scrolls the chat messages container
  const scrollChatToBottom = () => {
    if (chatMessagesRef.current) {
      setTimeout(() => {
        if (chatMessagesRef.current) {
          chatMessagesRef.current.scrollTop = chatMessagesRef.current.scrollHeight;
        }
      }, 100);
    }
  };

  // Scroll after messages change
  useEffect(() => {
    if (chatMessages.length > 0) {
      scrollChatToBottom();
    }
  }, [chatMessages]);

  useEffect(() => {
    if (isOpen) {
      // Initialize with a welcome message based on incident type
      const incidentType = getIncidentType();
      const TypeIcon = getTypeIcon(incidentType);
      
      let welcomeMessage;
      if (isSafetyIncident()) {
        welcomeMessage = (
          <div className="space-y-3">
            <div className="flex items-center gap-2">
              <Shield className="h-5 w-5 text-red-600" />
              <span className="font-semibold text-red-700">SAFETY ALERT - Priority Response Required</span>
            </div>
            <p>
              I'm your AI safety assistant ready to help resolve safety incident {incident.id}. 
              Safety is our top priority. I can provide emergency protocols, safety procedures, 
              coordinate with safety teams, and guide you through proper safety resolution steps.
            </p>
            <div className="bg-red-50 dark:bg-red-950/20 p-3 rounded-md border border-red-200 dark:border-red-800">
              <p className="text-sm text-red-700 dark:text-red-300">
                🚨 Remember: If there's immediate danger to personnel, evacuate the area and call emergency services first.
              </p>
            </div>
            <p>How would you like to proceed with this safety incident?</p>
          </div>
        );
      } else {
        welcomeMessage = `Hello! I'm your AI assistant ready to help resolve ${incidentType} incident ${incident.id}. I can analyze the issue, suggest solutions, and help you coordinate the resolution process. How would you like to proceed?`;
      }
      
      setChatMessages([
        {
          id: '1',
          type: 'ai',
          message: welcomeMessage,
          timestamp: new Date(),
        }
      ]);
      // Reset resolution data
      setResolutionNotes('');
    } else {
      setChatMessages([]);
      setInputMessage('');
      setIsProcessing(false);
    }
  }, [isOpen, incident.id]);

  const getSeverityBadgeVariant = (severity: string): "default" | "secondary" | "destructive" | "outline" => {
    switch (severity) {
      case 'critical': return 'destructive';
      case 'high': return 'secondary';
      case 'medium': return 'default';
      case 'low': return 'outline';
      default: return 'outline';
    }
  };

  const getStatusBadgeVariant = (status: string): "default" | "secondary" | "destructive" | "outline" => {
    switch (status) {
      case 'resolved': return 'default';
      case 'in-progress': return 'secondary';
      case 'open': return 'destructive';
      default: return 'outline';
    }
  };

  const handleSendMessage = async () => {
    if (!inputMessage.trim() || isProcessing) return;

    const userMessage: ChatMessage = {
      id: Date.now().toString(),
      type: 'user',
      message: inputMessage,
      timestamp: new Date(),
    };

    setChatMessages(prev => [...prev, userMessage]);
    setInputMessage('');
    setIsProcessing(true);

    // Simulate AI processing
    setTimeout(() => {
      const aiResponse: ChatMessage = {
        id: (Date.now() + 1).toString(),
        type: 'ai',
        message: generateAIResponse(inputMessage, incident),
        timestamp: new Date(),
      };

      setChatMessages(prev => [...prev, aiResponse]);
      setIsProcessing(false);
    }, 1500);
  };

  const handleQuickAction = async (actionType: string) => {
    setIsProcessing(true);
    
    let actionMessage = '';
    let aiResponse: React.ReactNode = '';
    const incidentType = getIncidentType();
    
    switch (actionType) {
      case 'research':
        actionMessage = 'Research this incident and provide analysis';
        if (isSafetyIncident()) {
          aiResponse = (
            <div className="space-y-4">
              <div className="flex items-center gap-2">
                <Shield className="h-5 w-5 text-red-600" />
                <h3 className="font-semibold text-base text-red-700">🚨 Safety Incident Analysis for {incident.id}</h3>
              </div>
              
              <div className="bg-red-50 dark:bg-red-950/20 p-3 rounded-md border border-red-200 dark:border-red-800">
                <h4 className="font-medium text-sm mb-1 text-red-700 dark:text-red-300">Immediate Safety Assessment:</h4>
                <p className="text-sm text-red-600 dark:text-red-400">
                  High-priority safety incident requiring immediate attention and proper safety protocols.
                </p>
              </div>

              <div>
                <h4 className="font-medium text-sm mb-2 text-red-600">Safety Risk Analysis:</h4>
                <div className="space-y-1 text-sm">
                  <div className="flex items-start gap-2">
                    <span className="font-medium text-red-600">Personnel Risk:</span>
                    <span className="text-muted-foreground">Potential exposure to hazardous conditions</span>
                  </div>
                  <div className="flex items-start gap-2">
                    <span className="font-medium text-orange-600">Equipment Risk:</span>
                    <span className="text-muted-foreground">Safety systems may be compromised</span>
                  </div>
                  <div className="flex items-start gap-2">
                    <span className="font-medium text-yellow-600">Environmental:</span>
                    <span className="text-muted-foreground">Check for environmental hazards</span>
                  </div>
                </div>
              </div>

              <div>
                <h4 className="font-medium text-sm mb-2 text-orange-600">Emergency Response Actions:</h4>
                <div className="text-sm space-y-1">
                  <div>• Secure the area and restrict access</div>
                  <div>• Notify safety officer and emergency team</div>
                  <div>• Implement lockout/tagout procedures if applicable</div>
                  <div>• Document all safety measures taken</div>
                </div>
              </div>

              <div className="bg-yellow-50 dark:bg-yellow-950/20 p-3 rounded-md border border-yellow-200 dark:border-yellow-800">
                <h4 className="font-medium text-sm mb-1 text-yellow-700 dark:text-yellow-300">Safety Compliance:</h4>
                <p className="text-sm text-yellow-600 dark:text-yellow-400">
                  Ensure all actions comply with OSHA standards and company safety policies.
                </p>
              </div>
            </div>
          );
        } else {
          // Original process incident analysis
          aiResponse = (
            <div className="space-y-4">
              <div>
                <h3 className="font-semibold text-base mb-2">🔍 Incident Analysis for {incident.id}</h3>
              </div>
              
              <div>
                <h4 className="font-medium text-sm mb-1 text-orange-600">Root Cause Analysis:</h4>
                <p className="text-sm text-muted-foreground">
                  Based on the incident description and manufacturing context, this appears to be related to equipment malfunction in the production line.
                </p>
              </div>

              <div>
                <h4 className="font-medium text-sm mb-2 text-blue-600">Recommended Actions:</h4>
                <div className="space-y-1 text-sm">
                  <div className="flex items-start gap-2">
                    <span className="font-medium text-red-600">Immediate:</span>
                    <span className="text-muted-foreground">Isolate affected equipment to prevent cascade failures</span>
                  </div>
                  <div className="flex items-start gap-2">
                    <span className="font-medium text-orange-600">Short-term:</span>
                    <span className="text-muted-foreground">Deploy backup systems if available</span>
                  </div>
                  <div className="flex items-start gap-2">
                    <span className="font-medium text-green-600">Long-term:</span>
                    <span className="text-muted-foreground">Schedule maintenance during next planned downtime</span>
                  </div>
                </div>
              </div>

              <div>
                <h4 className="font-medium text-sm mb-2 text-purple-600">Risk Assessment:</h4>
                <div className="text-sm space-y-1">
                  <div>• Current impact: <span className="font-medium">{incident.severity} priority</span></div>
                  <div>• Potential escalation: <span className="text-orange-600">Equipment damage if not addressed within 2 hours</span></div>
                  <div>• Affected systems: <span className="font-medium">Production line A, Quality control sensors</span></div>
                </div>
              </div>

              <div className="bg-blue-50 dark:bg-blue-950/20 p-3 rounded-md border border-blue-200 dark:border-blue-800">
                <h4 className="font-medium text-sm mb-1 text-blue-700 dark:text-blue-300">Similar Incidents:</h4>
                <p className="text-sm text-blue-600 dark:text-blue-400">
                  Found 3 similar incidents in the past 6 months with average resolution time of 45 minutes.
                </p>
              </div>
            </div>
          );
        }
        break;
      case 'resolve':
        actionMessage = 'Provide step-by-step resolution guide';
        if (isSafetyIncident()) {
          aiResponse = (
            <div className="space-y-4">
              <div className="flex items-center gap-2">
                <Shield className="h-5 w-5 text-red-600" />
                <h3 className="font-semibold text-base text-red-700">🚨 Safety Resolution Protocol for {incident.id}</h3>
              </div>
              
              <div className="space-y-3">
                <div className="border-l-4 border-red-500 pl-3 bg-red-50 dark:bg-red-950/20 p-3 rounded-r-md">
                  <h4 className="font-medium text-sm text-red-600">Step 1: Emergency Safety Check</h4>
                  <ul className="text-sm text-red-700 dark:text-red-300 mt-1 space-y-1">
                    <li>• Ensure all personnel are at safe distance (minimum 50ft)</li>
                    <li>• Verify emergency evacuation routes are clear</li>
                    <li>• Confirm emergency response team is notified</li>
                    <li>• Check for immediate hazards (gas leaks, electrical, chemical)</li>
                  </ul>
                </div>

                <div className="border-l-4 border-orange-500 pl-3 bg-orange-50 dark:bg-orange-950/20 p-3 rounded-r-md">
                  <h4 className="font-medium text-sm text-orange-600">Step 2: Secure the Area</h4>
                  <ul className="text-sm text-orange-700 dark:text-orange-300 mt-1 space-y-1">
                    <li>• Implement lockout/tagout procedures</li>
                    <li>• Set up safety barriers and warning signs</li>
                    <li>• Isolate energy sources (electrical, pneumatic, hydraulic)</li>
                    <li>• Document all safety measures taken</li>
                  </ul>
                </div>

                <div className="border-l-4 border-blue-500 pl-3 bg-blue-50 dark:bg-blue-950/20 p-3 rounded-r-md">
                  <h4 className="font-medium text-sm text-blue-600">Step 3: Safety Assessment</h4>
                  <ul className="text-sm text-blue-700 dark:text-blue-300 mt-1 space-y-1">
                    <li>• Conduct safety inspection with qualified personnel</li>
                    <li>• Use appropriate PPE and safety equipment</li>
                    <li>• Test for environmental hazards (air quality, temperature)</li>
                    <li>• Review safety data sheets if chemicals involved</li>
                  </ul>
                </div>

                <div className="border-l-4 border-green-500 pl-3 bg-green-50 dark:bg-green-950/20 p-3 rounded-r-md">
                  <h4 className="font-medium text-sm text-green-600">Step 4: Controlled Resolution</h4>
                  <ul className="text-sm text-green-700 dark:text-green-300 mt-1 space-y-1">
                    <li>• Follow approved safety procedures</li>
                    <li>• Work with safety officer supervision</li>
                    <li>• Implement corrective actions gradually</li>
                    <li>• Verify safety systems before resuming operations</li>
                  </ul>
                </div>
              </div>

              <div className="bg-red-50 dark:bg-red-950/20 p-3 rounded-md border border-red-200 dark:border-red-800">
                <div className="text-sm space-y-1">
                  <div><span className="font-medium text-red-700 dark:text-red-300">Safety Officer Required:</span> Yes - Must supervise all steps</div>
                  <div><span className="font-medium text-red-700 dark:text-red-300">Required PPE:</span> Hard hat, safety glasses, steel-toed boots, high-vis vest</div>
                  <div><span className="font-medium text-red-700 dark:text-red-300">Emergency Contact:</span> Safety Hotline: ext. 911</div>
                </div>
              </div>
            </div>
          );
        } else {
          // Original process resolution steps
          aiResponse = (
            <div className="space-y-4">
              <div>
                <h3 className="font-semibold text-base mb-2">⚡ Resolution Steps for {incident.id}</h3>
              </div>
              
              <div className="space-y-3">
                <div className="border-l-4 border-red-500 pl-3">
                  <h4 className="font-medium text-sm text-red-600">Step 1: Safety Check</h4>
                  <ul className="text-sm text-muted-foreground mt-1 space-y-1">
                    <li>• Ensure all personnel are at safe distance</li>
                    <li>• Verify emergency systems are operational</li>
                  </ul>
                </div>

                <div className="border-l-4 border-orange-500 pl-3">
                  <h4 className="font-medium text-sm text-orange-600">Step 2: System Isolation</h4>
                  <ul className="text-sm text-muted-foreground mt-1 space-y-1">
                    <li>• Power down affected equipment using emergency stop</li>
                    <li>• Activate bypass systems to maintain production flow</li>
                  </ul>
                </div>

                <div className="border-l-4 border-blue-500 pl-3">
                  <h4 className="font-medium text-sm text-blue-600">Step 3: Diagnostic</h4>
                  <ul className="text-sm text-muted-foreground mt-1 space-y-1">
                    <li>• Run diagnostic sequence: <code className="bg-muted px-1 rounded">DIAG-{incident.id}</code></li>
                    <li>• Check sensor readings for anomalies</li>
                    <li>• Review recent maintenance logs</li>
                  </ul>
                </div>

                <div className="border-l-4 border-green-500 pl-3">
                  <h4 className="font-medium text-sm text-green-600">Step 4: Resolution</h4>
                  <ul className="text-sm text-muted-foreground mt-1 space-y-1">
                    <li>• Replace faulty component (estimated time: 30 minutes)</li>
                    <li>• Recalibrate sensors</li>
                    <li>• Perform functionality test</li>
                  </ul>
                </div>
              </div>

              <div className="bg-green-50 dark:bg-green-950/20 p-3 rounded-md border border-green-200 dark:border-green-800">
                <div className="text-sm space-y-1">
                  <div><span className="font-medium text-green-700 dark:text-green-300">Estimated Resolution Time:</span> 45-60 minutes</div>
                  <div><span className="font-medium text-green-700 dark:text-green-300">Required Resources:</span> Maintenance team (2 people), spare parts kit B</div>
                </div>
              </div>
            </div>
          );
        }
        break;
      case 'escalate':
        actionMessage = 'Escalate this incident to appropriate teams';
        if (isSafetyIncident()) {
          aiResponse = (
            <div className="space-y-4">
              <div className="flex items-center gap-2">
                <Shield className="h-5 w-5 text-red-600" />
                <h3 className="font-semibold text-base text-red-700">🚨 Safety Escalation Protocol for {incident.id}</h3>
              </div>
              
              <div className="space-y-3">
                <div className="bg-red-50 dark:bg-red-950/20 p-3 rounded-md border border-red-200 dark:border-red-800">
                  <h4 className="font-medium text-sm text-red-700 dark:text-red-300 mb-2">Emergency Safety Escalation:</h4>
                  <div className="text-sm space-y-1">
                    <div>• Safety Director: <span className="font-medium">Robert Davis</span></div>
                    <div>• Emergency Contact: <span className="font-mono text-red-600">+1-555-SAFETY (911)</span></div>
                    <div>• Response time: <span className="font-medium">Immediate</span></div>
                    <div>• Authority: Can authorize emergency shutdown</div>
                  </div>
                </div>

                <div className="bg-orange-50 dark:bg-orange-950/20 p-3 rounded-md border border-orange-200 dark:border-orange-800">
                  <h4 className="font-medium text-sm text-orange-700 dark:text-orange-300 mb-2">Plant Safety Manager:</h4>
                  <div className="text-sm space-y-1">
                    <div>• Manager: <span className="font-medium">Lisa Thompson</span></div>
                    <div>• Contact: <span className="font-mono">ext. 2911</span> / <span className="text-blue-600"><EMAIL></span></div>
                    <div>• Role: Safety investigation and compliance</div>
                  </div>
                </div>

                <div className="bg-yellow-50 dark:bg-yellow-950/20 p-3 rounded-md border border-yellow-200 dark:border-yellow-800">
                  <h4 className="font-medium text-sm text-yellow-700 dark:text-yellow-300 mb-2">OSHA Coordinator:</h4>
                  <div className="text-sm space-y-1">
                    <div>• Coordinator: <span className="font-medium">Mark Wilson</span></div>
                    <div>• Contact: <span className="font-mono">ext. 3911</span></div>
                    <div>• Required for: Reportable incidents and compliance issues</div>
                  </div>
                </div>
              </div>

              <div className="bg-green-50 dark:bg-green-950/20 p-3 rounded-md border border-green-200 dark:border-green-800">
                <div className="text-sm space-y-1 text-green-700 dark:text-green-300">
                  <div>✅ Safety team notifications: Sent automatically</div>
                  <div>✅ Emergency protocols: Activated</div>
                  <div>✅ Incident documentation: Initiated</div>
                </div>
              </div>
            </div>
          );
        } else {
          // Original escalation response
          aiResponse = (
            <div className="space-y-4">
              <div>
                <h3 className="font-semibold text-base mb-2">📞 Escalation Plan for {incident.id}</h3>
              </div>
              
              <div className="space-y-3">
                <div className="bg-yellow-50 dark:bg-yellow-950/20 p-3 rounded-md border border-yellow-200 dark:border-yellow-800">
                  <h4 className="font-medium text-sm text-yellow-700 dark:text-yellow-300 mb-2">Primary Escalation:</h4>
                  <div className="text-sm space-y-1">
                    <div>• Manager: <span className="font-medium">John Smith (Production Manager)</span></div>
                    <div>• Contact: <span className="font-mono">ext. 1234</span> / <span className="text-blue-600"><EMAIL></span></div>
                    <div>• Expected response time: <span className="font-medium">15 minutes</span></div>
                  </div>
                </div>

                <div className="bg-blue-50 dark:bg-blue-950/20 p-3 rounded-md border border-blue-200 dark:border-blue-800">
                  <h4 className="font-medium text-sm text-blue-700 dark:text-blue-300 mb-2">Secondary Escalation:</h4>
                  <div className="text-sm space-y-1">
                    <div>• Engineering Team Lead: <span className="font-medium">Sarah Johnson</span></div>
                    <div>• Contact: <span className="font-mono">ext. 5678</span> / <span className="text-blue-600"><EMAIL></span></div>
                    <div>• For technical assistance and advanced diagnostics</div>
                  </div>
                </div>

                <div className="bg-red-50 dark:bg-red-950/20 p-3 rounded-md border border-red-200 dark:border-red-800">
                  <h4 className="font-medium text-sm text-red-700 dark:text-red-300 mb-2">Emergency Escalation:</h4>
                  <div className="text-sm space-y-1">
                    <div>• Plant Director: <span className="font-medium">Mike Wilson</span></div>
                    <div>• Contact: <span className="font-mono text-red-600">+1-555-0123</span></div>
                    <div>• Only for critical safety concerns</div>
                  </div>
                </div>
              </div>

              <div className="bg-green-50 dark:bg-green-950/20 p-3 rounded-md border border-green-200 dark:border-green-800">
                <div className="text-sm space-y-1 text-green-700 dark:text-green-300">
                  <div>✅ Notification sent to: All relevant stakeholders</div>
                  <div>✅ Escalation triggered: Auto-notifications enabled</div>
                </div>
              </div>
            </div>
          );
        }
        break;
      default:
        actionMessage = actionType;
        if (actionType === 'analyze') {
          actionMessage = 'Analyze this specific incident and provide concrete measures';
          if (isSafetyIncident()) {
            aiResponse = (
              <div className="space-y-4">
                <div className="flex items-center gap-2">
                  <Shield className="h-5 w-5 text-red-600" />
                  <h3 className="font-semibold text-base text-red-700">🔍 Incident Analysis: {incident.id}</h3>
                </div>
                
                <div className="bg-red-50 dark:bg-red-950/20 p-3 rounded-md border border-red-200 dark:border-red-800">
                  <h4 className="font-medium text-sm mb-2 text-red-700 dark:text-red-300">📋 Incident Summary:</h4>
                  <p className="text-sm text-red-600 dark:text-red-400">
                    Overhead crane carrying 2-ton steel beam with workers positioned directly underneath load path. 
                    Missing safety barriers, unstable load, and personnel in immediate crush hazard zone.
                  </p>
                </div>

                <div className="bg-orange-50 dark:bg-orange-950/20 p-3 rounded-md border border-orange-200 dark:border-orange-800">
                  <h4 className="font-medium text-sm mb-2 text-orange-700 dark:text-orange-300">⚠️ Critical Risk Factors:</h4>
                  <div className="text-sm space-y-1 text-orange-600 dark:text-orange-400">
                    <div>• <strong>Crush Hazard:</strong> 2-ton suspended load directly above personnel</div>
                    <div>• <strong>Equipment Failure Risk:</strong> Load observed swaying and unstable</div>
                    <div>• <strong>Human Factor:</strong> Workers focused on tasks, not monitoring overhead hazards</div>
                    <div>• <strong>System Failure:</strong> Complete absence of safety barriers and warning systems</div>
                  </div>
                </div>

                <div className="bg-blue-50 dark:bg-blue-950/20 p-3 rounded-md border border-blue-200 dark:border-blue-800">
                  <h4 className="font-medium text-sm mb-2 text-blue-700 dark:text-blue-300">🎯 Immediate Measures Required:</h4>
                  <div className="text-sm space-y-1 text-blue-600 dark:text-blue-400">
                    <div>1. <strong>IMMEDIATE:</strong> Stop all crane operations, evacuate danger zone (50ft radius)</div>
                    <div>2. <strong>SECURE LOAD:</strong> Lower load to ground using controlled procedures</div>
                    <div>3. <strong>INSTALL BARRIERS:</strong> Set up safety tape and physical barriers around entire crane operation area</div>
                    <div>4. <strong>ESTABLISH SPOTTER:</strong> Assign dedicated safety observer for all crane movements</div>
                    <div>5. <strong>RETRAIN PERSONNEL:</strong> Emergency safety briefing on crane operation hazards</div>
                  </div>
                </div>

                <div className="bg-green-50 dark:bg-green-950/20 p-3 rounded-md border border-green-200 dark:border-green-800">
                  <h4 className="font-medium text-sm mb-2 text-green-700 dark:text-green-300">📝 Long-term Preventive Actions:</h4>
                  <div className="text-sm space-y-1 text-green-600 dark:text-green-400">
                    <div>• Install permanent crane operation warning systems (lights/alarms)</div>
                    <div>• Implement mandatory crane operation safety protocols</div>
                    <div>• Conduct monthly crane safety audits and inspections</div>
                    <div>• Update safety procedures to include mandatory barrier setup</div>
                  </div>
                </div>

                <div className="bg-yellow-50 dark:bg-yellow-950/20 p-3 rounded-md border border-yellow-200 dark:border-yellow-800">
                  <h4 className="font-medium text-sm mb-2 text-yellow-700 dark:text-yellow-300">📊 Compliance Impact:</h4>
                  <p className="text-sm text-yellow-600 dark:text-yellow-400">
                    This incident constitutes a serious OSHA violation under 29 CFR 1926.1400 (Crane and Derrick Standards). 
                    Immediate corrective action required to avoid potential citations and ensure worker safety.
                  </p>
                </div>
              </div>
            );
          } else {
            // Standard process incident analysis
            aiResponse = `Analyzing the specific details of ${incident.id}: ${incident.description}. This appears to be equipment-related with potential for process disruption. Recommend immediate isolation and diagnostic procedures.`;
          }
        } else {
          aiResponse = `Processing your request for "${actionType}". Let me analyze the current situation and provide recommendations.`;
        }
    }

    const userMessage: ChatMessage = {
      id: Date.now().toString(),
      type: 'user',
      message: actionMessage,
      timestamp: new Date(),
    };

    setTimeout(() => {
      const aiMessage: ChatMessage = {
        id: (Date.now() + 1).toString(),
        type: 'ai',
        message: aiResponse,
        timestamp: new Date(),
      };

      setChatMessages(prev => [...prev, userMessage, aiMessage]);
      setIsProcessing(false);
    }, 2000);
  };

  const generateAIResponse = (userInput: string, incident: Incident): string | React.ReactNode => {
    const input = userInput.toLowerCase();
    const incidentType = getIncidentType();
    const isSafety = isSafetyIncident();
    
    // Handle progress/status questions
    if (input.includes('progress') || input.includes('status') || input.includes('what have') || input.includes('done so far')) {
      if (isSafety) {
        return (
          <div className="space-y-3">
            <div className="flex items-center gap-2">
              <Shield className="h-4 w-4 text-red-600" />
              <span className="font-semibold text-red-700">Safety Incident Progress Summary</span>
            </div>
            
            <div className="bg-green-50 dark:bg-green-950/20 p-3 rounded-md border border-green-200 dark:border-green-800">
              <h4 className="font-medium text-sm mb-2 text-green-700 dark:text-green-300">✅ Actions Completed:</h4>
              <div className="text-sm space-y-1 text-green-600 dark:text-green-400">
                <div>• <strong>IMMEDIATE RESPONSE:</strong> Safety Officer Martinez alerted and responded</div>
                <div>• <strong>AREA SECURED:</strong> Work area cleared, crane operations halted</div>
                <div>• <strong>SAFETY MEASURES:</strong> Barriers installed, warning signs deployed</div>
                <div>• <strong>EQUIPMENT ISOLATED:</strong> Crane locked out, load secured to ground</div>
                <div>• <strong>PERSONNEL SAFETY:</strong> All workers relocated to safe zones</div>
                <div>• <strong>DOCUMENTATION:</strong> Incident report created (SAFETY-2024-001)</div>
                <div>• <strong>COMPLIANCE:</strong> OSHA notification initiated by Safety Director</div>
              </div>
            </div>

            <div className="bg-blue-50 dark:bg-blue-950/20 p-3 rounded-md border border-blue-200 dark:border-blue-800">
              <h4 className="font-medium text-sm mb-2 text-blue-700 dark:text-blue-300">📋 Current Status:</h4>
              <div className="text-sm space-y-1 text-blue-600 dark:text-blue-400">
                <div>• Incident severity: <strong>CRITICAL</strong> - High priority response</div>
                <div>• Area status: <strong>SECURED</strong> - No personnel access</div>
                <div>• Operations: <strong>SUSPENDED</strong> - All crane ops halted</div>
                <div>• Investigation: <strong>IN PROGRESS</strong> - Safety audit underway</div>
              </div>
            </div>

            <div className="bg-orange-50 dark:bg-orange-950/20 p-3 rounded-md border border-orange-200 dark:border-orange-800">
              <h4 className="font-medium text-sm mb-2 text-orange-700 dark:text-orange-300">⏳ Next Steps:</h4>
              <div className="text-sm space-y-1 text-orange-600 dark:text-orange-400">
                <div>• Complete safety audit and investigation</div>
                <div>• Implement permanent safety improvements</div>
                <div>• Conduct mandatory safety retraining</div>
                <div>• Review and update crane operation procedures</div>
              </div>
            </div>
          </div>
        );
      } else {
        return `Progress update for ${incident.id}: Initial assessment completed, diagnostics running, backup systems activated. Maintenance team is working on resolution. Estimated completion time: 45-60 minutes. Would you like more specific details about any aspect?`;
      }
    }
    
    // Handle time/timeline questions
    if (input.includes('time') || input.includes('when') || input.includes('how long')) {
      if (isSafety) {
        return `Timeline for safety incident ${incident.id}: Reported at 14:22, immediate response within 3 minutes, area secured by 14:25. Safety audit is ongoing with no set completion time - safety takes priority over speed. Full operations resumption pending safety clearance.`;
      } else {
        return `Timeline for ${incident.id}: Reported this morning, response time under 15 minutes. Current estimated resolution: 45-60 minutes. Backup systems are maintaining operations at 80% capacity.`;
      }
    }
    
    // Handle team/people questions
    if (input.includes('team') || input.includes('who') || input.includes('assigned')) {
      if (isSafety) {
        return (
          <div className="space-y-3">
            <h4 className="font-medium text-red-700">🛡️ Safety Response Team Active:</h4>
            <div className="bg-red-50 dark:bg-red-950/20 p-3 rounded-md border border-red-200 dark:border-red-800">
              <div className="text-sm space-y-2 text-red-600 dark:text-red-400">
                <div><strong>Safety Officer Martinez</strong> - Primary responder, on-site coordination</div>
                <div><strong>Safety Director Johnson</strong> - Incident oversight, OSHA compliance</div>
                <div><strong>Crane Supervisor</strong> - Equipment isolation and investigation</div>
                <div><strong>Emergency Response Team</strong> - Standby for medical support</div>
              </div>
            </div>
            <p className="text-sm text-muted-foreground">All safety personnel are coordinating to ensure comprehensive incident resolution.</p>
          </div>
        );
      } else {
        return `Team assignment for ${incident.id}: Primary - ${incident.assignedTeam}, with Engineering support. John Doe leading diagnostics, Sarah Smith handling backup systems. Full team of 4 technicians actively working on resolution.`;
      }
    }
    
    // Handle safety-specific questions
    if (input.includes('safe') || input.includes('danger') || input.includes('risk')) {
      if (isSafety) {
        return `Safety status: AREA SECURED ✅ - All personnel evacuated from danger zone, 50ft safety perimeter established. Crane load lowered and secured. No injuries reported. Ongoing monitoring by safety personnel. Area will remain restricted until full safety clearance.`;
      } else {
        return `Safety assessment for ${incident.id}: No immediate safety risks detected. Standard safety protocols in place. Equipment isolated properly. Personnel using appropriate PPE.`;
      }
    }
    
    // Handle what/description questions
    if (input.includes('what happened') || input.includes('describe') || input.includes('details')) {
      return `Incident details: ${incident.description} This ${isSafety ? 'safety violation' : 'process incident'} requires ${isSafety ? 'immediate safety response' : 'technical resolution'}. Would you like me to provide specific analysis or resolution steps?`;
    }
    
    // Handle help/assistance questions
    if (input.includes('help') || input.includes('assist') || input.includes('support')) {
      if (isSafety) {
        return `I'm here to help with this critical safety incident. I can provide safety analysis, emergency protocols, coordinate with safety teams, track compliance requirements, and guide resolution steps. What specific assistance do you need?`;
      } else {
        return `I can help with incident analysis, troubleshooting steps, team coordination, documentation, and resolution tracking. What would you like me to focus on for this ${incidentType} incident?`;
      }
    }
    
    // Default contextual responses
    const defaultResponses = [
      `I'm analyzing ${incident.severity} severity incident "${incident.title}". Based on similar cases, I recommend ${isSafety ? 'prioritizing safety protocols and immediate hazard control' : 'systematic diagnostics and equipment isolation'}. Would you like detailed guidance?`,
      `For incident ${incident.id}, the current priority is ${isSafety ? 'ensuring personnel safety and regulatory compliance' : 'minimizing downtime while maintaining safety standards'}. I can provide step-by-step assistance. What's your immediate concern?`,
      `I've noted this incident is assigned to ${incident.assignedTeam}. ${isSafety ? 'Safety incidents require coordinated response across multiple teams' : 'I can help coordinate resolution efforts and provide technical guidance'}. How can I assist you further?`,
    ];
    
    return defaultResponses[Math.floor(Math.random() * defaultResponses.length)];
  };

  // Convert React node to plain text for textarea
  const convertToPlainText = (content: React.ReactNode): string => {
    if (typeof content === 'string') {
      return content;
    }
    
    // Simple text extraction - just get the text content
    const getText = (node: any): string => {
      if (typeof node === 'string') return node;
      if (typeof node === 'number') return node.toString();
      if (Array.isArray(node)) return node.map(getText).join(' ');
      if (node?.props?.children) return getText(node.props.children);
      return '';
    };
    
    return getText(content);
  };

  // Generate complete resolution notes from chat history
  const generateResolutionNotes = () => {
    if (chatMessages.length === 0) {
      return '';
    }

    let notes = `INCIDENT RESOLUTION NOTES - ${incident.id}\n`;
    notes += `=====================================\n\n`;
    notes += `Incident: ${incident.title}\n`;
    notes += `Status: ${incident.status}\n`;
    notes += `Severity: ${incident.severity}\n`;
    notes += `Assigned Team: ${incident.assignedTeam}\n`;
    notes += `Description: ${incident.description}\n\n`;
    
    notes += `CHAT HISTORY & AI ANALYSIS:\n`;
    notes += `----------------------------\n\n`;

    chatMessages.forEach((message, index) => {
      const timestamp = format(message.timestamp, 'HH:mm');
      const sender = message.type === 'user' ? 'USER' : 'AI ASSISTANT';
      
      notes += `[${timestamp}] ${sender}:\n`;
      
      if (typeof message.message === 'string') {
        notes += `${message.message}\n\n`;
      } else {
        // Convert React content to readable text
        const textContent = convertToPlainText(message.message);
        notes += `${textContent}\n\n`;
      }
    });

    notes += `\nRESOLUTION SUMMARY:\n`;
    notes += `------------------\n`;
    notes += `• Chat session completed with AI assistance\n`;
    notes += `• All analysis and recommendations documented above\n`;
    notes += `• Ready for implementation or further review\n\n`;

    return notes;
  };

  // Function to apply resolution - copies chat data to resolution notes
  const handleApplyResolution = () => {
    const chatData = generateResolutionNotes();
    setResolutionNotes(chatData);
    setActiveTab('resolution');
    showToast('Chat data copied to resolution notes');
  };

  const handleAssignTicket = () => {
    setActiveTab('jira');
  };

  // Function to check if message should show action buttons
  const shouldShowActionButtons = (message: ChatMessage) => {
    return message.type === 'ai' && 
           typeof message.message !== 'string' &&
           message.id !== '1'; // Don't show on welcome message
  };

  // Media display components for safety alerts
  const VideoPlayer: React.FC<{ video: { url: string; title: string; description?: string; thumbnail?: string } }> = ({ video }) => {
    const [isPlaying, setIsPlaying] = useState(false);
    const videoRef = useRef<HTMLVideoElement>(null);

    const togglePlay = () => {
      if (videoRef.current) {
        if (isPlaying) {
          videoRef.current.pause();
        } else {
          videoRef.current.play();
        }
        setIsPlaying(!isPlaying);
      }
    };

    return (
      <div className="border rounded-lg overflow-hidden bg-black">
        <div className="relative">
          <video
            ref={videoRef}
            className="w-full h-48 object-cover"
            poster={video.thumbnail}
            onPlay={() => setIsPlaying(true)}
            onPause={() => setIsPlaying(false)}
          >
            <source src={video.url} type="video/mp4" />
            Your browser does not support the video tag.
          </video>
          <div className="absolute inset-0 flex items-center justify-center">
            <Button
              size="lg"
              variant="secondary"
              className="opacity-80 hover:opacity-100"
              onClick={togglePlay}
            >
              {isPlaying ? <Pause className="h-6 w-6" /> : <Play className="h-6 w-6" />}
            </Button>
          </div>
        </div>
        <div className="p-3 bg-white dark:bg-gray-900">
          <h4 className="font-medium text-sm">{video.title}</h4>
          {video.description && (
            <p className="text-xs text-muted-foreground mt-1">{video.description}</p>
          )}
        </div>
      </div>
    );
  };

  const ImageGallery: React.FC<{ images: { url: string; title: string; description?: string }[] }> = ({ images }) => {
    const [selectedImage, setSelectedImage] = useState<number | null>(null);

    return (
      <div className="space-y-2">
        <div className="grid grid-cols-2 gap-2">
          {images.map((image, index) => (
            <div
              key={index}
              className="border rounded-lg overflow-hidden cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => setSelectedImage(index)}
            >
              <img
                src={image.url}
                alt={image.title}
                className="w-full h-24 object-cover"
              />
              <div className="p-2">
                <h5 className="text-xs font-medium truncate">{image.title}</h5>
              </div>
            </div>
          ))}
        </div>
        
        {selectedImage !== null && (
          <div className="fixed inset-0 z-50 bg-black/80 flex items-center justify-center" onClick={() => setSelectedImage(null)}>
            <div className="max-w-4xl max-h-[90vh] relative">
              <img
                src={images[selectedImage].url}
                alt={images[selectedImage].title}
                className="max-w-full max-h-full object-contain"
              />
              <Button
                size="sm"
                variant="secondary"
                className="absolute top-4 right-4"
                onClick={() => setSelectedImage(null)}
              >
                <X className="h-4 w-4" />
              </Button>
              <div className="absolute bottom-4 left-4 right-4 bg-black/70 text-white p-3 rounded">
                <h4 className="font-medium">{images[selectedImage].title}</h4>
                {images[selectedImage].description && (
                  <p className="text-sm mt-1">{images[selectedImage].description}</p>
                )}
              </div>
            </div>
          </div>
        )}
      </div>
    );
  };

  const DocumentList: React.FC<{ documents: { url: string; title: string; type: string }[] }> = ({ documents }) => {
    return (
      <div className="space-y-2">
        {documents.map((doc, index) => (
          <div key={index} className="flex items-center gap-3 p-2 border rounded-lg hover:bg-muted/50">
            <FileDown className="h-4 w-4 text-blue-600" />
            <div className="flex-1">
              <h5 className="text-sm font-medium">{doc.title}</h5>
              <p className="text-xs text-muted-foreground">{doc.type}</p>
            </div>
            <Button size="sm" variant="outline" asChild>
              <a href={doc.url} target="_blank" rel="noopener noreferrer">
                <ExternalLink className="h-3 w-3 mr-1" />
                Open
              </a>
            </Button>
          </div>
        ))}
      </div>
    );
  };

  const handleCreateJiraTicket = () => {
    // Simulate JIRA ticket creation
    const ticketPrefix = isSafetyIncident() ? 'SAFETY' : 'MAINT';
    const ticketId = `${ticketPrefix}-${Math.floor(Math.random() * 10000)}`;
    
    // Get assignee details
    const getAssigneeDetails = (assigneeValue: string) => {
      const assigneeProfiles: { [key: string]: { name: string; role: string; contact: string; expertise: string } } = {
        'safety.martinez': {
          name: 'Roberto Martinez',
          role: 'Senior Safety Officer',
          contact: 'ext. 2911 | <EMAIL>',
          expertise: 'Emergency Response, Hazmat Protocols, Incident Investigation'
        },
        'safety.director.johnson': {
          name: 'Director Sarah Johnson',
          role: 'Safety Director',
          contact: 'ext. 1911 | <EMAIL>',
          expertise: 'Safety Management Systems, Regulatory Compliance, Risk Assessment'
        },
        'safety.manager.thompson': {
          name: 'Lisa Thompson',
          role: 'Plant Safety Manager',
          contact: 'ext. 3911 | <EMAIL>',
          expertise: 'Safety Investigations, Training Programs, Process Safety'
        },
        'osha.coordinator.wilson': {
          name: 'Mark Wilson',
          role: 'OSHA Compliance Coordinator',
          contact: 'ext. 4911 | <EMAIL>',
          expertise: 'OSHA Standards, Regulatory Reporting, Compliance Audits'
        },
        'emergency.response.team': {
          name: 'Emergency Response Team',
          role: 'Emergency Response Unit',
          contact: 'Emergency Line: ext. 911',
          expertise: 'Emergency Response, First Aid, Crisis Management'
        },
        'safety.engineer.davis': {
          name: 'Dr. Jennifer Davis',
          role: 'Safety Engineer',
          contact: 'ext. 5911 | <EMAIL>',
          expertise: 'Safety Engineering, Risk Analysis, Safety Systems Design'
        },
        'safety.trainer.garcia': {
          name: 'Carlos Garcia',
          role: 'Safety Training Coordinator',
          contact: 'ext. 6911 | <EMAIL>',
          expertise: 'Safety Training, Behavior-based Safety, Safety Communication'
        }
      };
      
      return assigneeProfiles[assigneeValue] || {
        name: assigneeValue,
        role: 'Team Member',
        contact: 'Contact via JIRA',
        expertise: 'Various'
      };
    };

    const assigneeDetails = getAssigneeDetails(jiraAssignee);
    const ticketTypeLabel = isSafetyIncident() ? '🛡️ SAFETY' : 'JIRA';
    
    const successMessage: ChatMessage = {
      id: Date.now().toString(),
      type: 'ai',
      message: (
        <div className="space-y-3">
          <div className="flex items-center gap-2">
            <CheckCircle className="h-5 w-5 text-green-500" />
            <span className="font-semibold text-green-700">
              {ticketTypeLabel} ticket {ticketId} has been created successfully!
            </span>
          </div>
          
          <div className={`p-3 rounded-md border ${
            isSafetyIncident() 
              ? 'bg-red-50 dark:bg-red-950/20 border-red-200 dark:border-red-800' 
              : 'bg-green-50 dark:bg-green-950/20 border-green-200 dark:border-green-800'
          }`}>
            <h4 className="font-medium text-sm mb-2">Ticket Details:</h4>
            <div className="text-sm space-y-1">
              <div>• <span className="font-medium">ID:</span> {ticketId}</div>
              <div>• <span className="font-medium">Assignee:</span> {assigneeDetails.name}</div>
              <div>• <span className="font-medium">Role:</span> {assigneeDetails.role}</div>
              <div>• <span className="font-medium">Priority:</span> {jiraPriority}</div>
              <div>• <span className="font-medium">Status:</span> Open</div>
              <div>• <span className="font-medium">Link:</span> <a href="#" className="text-blue-600 hover:underline">https://company.atlassian.net/browse/{ticketId}</a></div>
            </div>
          </div>

          {isSafetyIncident() && (
            <div className="bg-yellow-50 dark:bg-yellow-950/20 p-3 rounded-md border border-yellow-200 dark:border-yellow-800">
              <h4 className="font-medium text-sm mb-2 text-yellow-700 dark:text-yellow-300">👤 Assignee Profile:</h4>
              <div className="text-sm space-y-1 text-yellow-600 dark:text-yellow-400">
                <div>• <span className="font-medium">Contact:</span> {assigneeDetails.contact}</div>
                <div>• <span className="font-medium">Expertise:</span> {assigneeDetails.expertise}</div>
                <div>• <span className="font-medium">Response Time:</span> Safety incidents - Immediate (&lt; 15 min)</div>
              </div>
            </div>
          )}

          <p className="text-sm text-muted-foreground">
            {isSafetyIncident() 
              ? 'Safety team member has been notified via emergency communication system and will receive all incident details automatically.'
              : 'The assignee has been notified and will receive all incident details automatically.'
            }
          </p>
        </div>
      ),
      timestamp: new Date(),
    };

    setChatMessages(prev => [...prev, successMessage]);
    setActiveTab('chat');
    showToast(`${ticketTypeLabel} ticket ${ticketId} created successfully!`);
  };

  // Additional action handlers for resolution tab
  const handleMarkResolved = () => {
    showToast('Incident marked as resolved successfully!');
  };

  const handleCopyToClipboard = () => {
    if (resolutionNotes) {
      navigator.clipboard.writeText(resolutionNotes);
      showToast('Resolution notes copied to clipboard');
    } else {
      showToast('No resolution notes to copy', 'info');
    }
  };

  const handleExportReport = () => {
    // Simulate export functionality
    showToast('Resolution report exported successfully');
  };

  if (!incident) return null;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-7xl h-[85vh] flex flex-col p-0">
        <DialogHeader className="px-6 py-4 border-b">
          <DialogTitle className="text-xl font-semibold flex items-center gap-2">
            {isSafetyIncident() ? (
              <>
                <Shield className="h-5 w-5 text-red-600" />
                <span className="text-red-700">Safety Alert Resolution Center</span>
                <span className="text-muted-foreground">- {incident.id}</span>
              </>
            ) : (
              <>
                AI-Powered Resolution Center - {incident.id}
              </>
            )}
          </DialogTitle>
        </DialogHeader>
        
        <div className="flex flex-1 overflow-hidden">
          {/* Left Panel - Incident Details */}
          <div className="w-1/2 border-r bg-muted/30">
            <ScrollArea className="h-full">
              <div className="p-6 space-y-6">
                {/* Header */}
                <div>
                  <h3 className="text-lg font-semibold mb-2">{incident.title}</h3>
                  <div className="flex items-center gap-2 mb-4">
                    <Badge variant={getStatusBadgeVariant(incident.status)} className="capitalize">
                      {incident.status.replace('-', ' ')}
                    </Badge>
                    <Badge variant={getSeverityBadgeVariant(incident.severity)} className="capitalize">
                      {incident.severity}
                    </Badge>
                    <Badge variant={getTypeBadgeVariant(getIncidentType())} className="capitalize flex items-center gap-1">
                      {React.createElement(getTypeIcon(getIncidentType()), { className: "h-3 w-3" })}
                      {getIncidentType()}
                    </Badge>
                  </div>
                </div>

                {/* Safety Media Content */}
                {isSafetyIncident() && incident.media && (
                  <>
                    {incident.media.videos && incident.media.videos.length > 0 && (
                      <Card>
                        <CardHeader>
                          <CardTitle className="text-sm flex items-center gap-2">
                            <Video className="h-4 w-4 text-red-600" />
                            Incident Footage
                          </CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-3">
                          {incident.media.videos.map((video, index) => (
                            <VideoPlayer key={index} video={video} />
                          ))}
                        </CardContent>
                      </Card>
                    )}

                    {incident.media.images && incident.media.images.length > 0 && (
                      <Card>
                        <CardHeader>
                          <CardTitle className="text-sm flex items-center gap-2">
                            <ImageIcon className="h-4 w-4 text-blue-600" />
                            Safety Images & Diagrams
                          </CardTitle>
                        </CardHeader>
                        <CardContent>
                          <ImageGallery images={incident.media.images} />
                        </CardContent>
                      </Card>
                    )}

                    {incident.media.documents && incident.media.documents.length > 0 && (
                      <Card>
                        <CardHeader>
                          <CardTitle className="text-sm flex items-center gap-2">
                            <FileText className="h-4 w-4 text-green-600" />
                            Safety Documentation
                          </CardTitle>
                        </CardHeader>
                        <CardContent>
                          <DocumentList documents={incident.media.documents} />
                        </CardContent>
                      </Card>
                    )}
                  </>
                )}

                {/* Key Details */}
                <Card>
                  <CardHeader>
                    <CardTitle className="text-sm">Incident Details</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <div className="flex items-center gap-2">
                      <Calendar className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm text-muted-foreground">Reported:</span>
                      <span className="text-sm">{format(parseISO(incident.reportedAt), 'MMM dd, yyyy HH:mm')}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <User className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm text-muted-foreground">Assigned Team:</span>
                      <span className="text-sm">{incident.assignedTeam}</span>
                    </div>
                    {incident.resolvedAt && (
                      <div className="flex items-center gap-2">
                        <CheckCircle className="h-4 w-4 text-green-500" />
                        <span className="text-sm text-muted-foreground">Resolved:</span>
                        <span className="text-sm">{format(parseISO(incident.resolvedAt), 'MMM dd, yyyy HH:mm')}</span>
                      </div>
                    )}
                  </CardContent>
                </Card>

                {/* Description */}
                <Card>
                  <CardHeader>
                    <CardTitle className="text-sm">Description</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-sm text-muted-foreground leading-relaxed">
                      {incident.description}
                    </p>
                  </CardContent>
                </Card>

                {/* Resolution Notes */}
                {incident.resolutionNotes && (
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-sm">Resolution Notes</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <p className="text-sm text-muted-foreground leading-relaxed bg-green-50 dark:bg-green-950/20 p-3 rounded border border-green-200 dark:border-green-800">
                        {incident.resolutionNotes}
                      </p>
                    </CardContent>
                  </Card>
                )}

                {/* Updates History */}
                <Card>
                  <CardHeader>
                    <CardTitle className="text-sm">Update History</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <ScrollArea className="h-40">
                      <div className="space-y-3">
                        {incident.updates && incident.updates.length > 0 ? (
                          incident.updates.map((update, index) => (
                            <div key={index} className="border-l-2 border-muted pl-3 pb-2">
                              <div className="flex items-center gap-2 mb-1">
                                <span className="text-xs font-medium">{update.author}</span>
                                <span className="text-xs text-muted-foreground">
                                  {format(parseISO(update.timestamp), 'MMM dd, HH:mm')}
                                </span>
                              </div>
                              <p className="text-xs text-muted-foreground">{update.note}</p>
                            </div>
                          ))
                        ) : (
                          <p className="text-xs text-center text-muted-foreground py-4">
                            No updates recorded yet.
                          </p>
                        )}
                      </div>
                    </ScrollArea>
                  </CardContent>
                </Card>
              </div>
            </ScrollArea>
          </div>

          {/* Right Panel - AI Chat Interface */}
          <div className="w-1/2 flex flex-col">
            <Tabs value={activeTab} onValueChange={setActiveTab} className="flex-1 flex flex-col">
              <TabsList className="grid w-full grid-cols-3 mx-4 mt-4 flex-shrink-0">
                <TabsTrigger value="chat" className="flex items-center gap-1">
                  <Bot className="h-4 w-4" />
                  AI Chat
                </TabsTrigger>
                <TabsTrigger value="resolution" className="flex items-center gap-1">
                  <CheckCircle className="h-4 w-4" />
                  Resolution
                </TabsTrigger>
                <TabsTrigger value="jira" className="flex items-center gap-1">
                  <ExternalLink className="h-4 w-4" />
                  JIRA
                </TabsTrigger>
              </TabsList>

              <TabsContent value="chat" className="flex-1 flex flex-col mt-4 mx-4 min-h-0">
                {/* Quick Actions */}
                <div className="mb-4 flex-shrink-0">
                  <h4 className="text-sm font-medium mb-2">Quick Actions</h4>
                  <div className="grid grid-cols-2 gap-2">
                    {isSafetyIncident() ? (
                      // Safety-specific quick actions
                      <>
                        <Button 
                          variant="outline" 
                          size="sm" 
                          onClick={() => handleQuickAction('research')}
                          disabled={isProcessing}
                          className="flex items-center gap-1 border-red-200 text-red-700 hover:bg-red-50"
                        >
                          <Shield className="h-4 w-4" />
                          Safety Analysis
                        </Button>
                        <Button 
                          variant="outline" 
                          size="sm" 
                          onClick={() => handleQuickAction('resolve')}
                          disabled={isProcessing}
                          className="flex items-center gap-1 border-orange-200 text-orange-700 hover:bg-orange-50"
                        >
                          <AlertTriangle className="h-4 w-4" />
                          Safety Protocol
                        </Button>
                        <Button 
                          variant="outline" 
                          size="sm" 
                          onClick={() => handleQuickAction('escalate')}
                          disabled={isProcessing}
                          className="flex items-center gap-1 border-red-200 text-red-700 hover:bg-red-50"
                        >
                          <User className="h-4 w-4" />
                          Safety Team
                        </Button>
                        <Button 
                          variant="outline" 
                          size="sm" 
                          onClick={() => handleQuickAction('analyze')}
                          disabled={isProcessing}
                          className="flex items-center gap-1 border-blue-200 text-blue-700 hover:bg-blue-50"
                        >
                          <Search className="h-4 w-4" />
                          Risk Assessment
                        </Button>
                      </>
                    ) : (
                      // Standard process incident actions
                      <>
                        <Button 
                          variant="outline" 
                          size="sm" 
                          onClick={() => handleQuickAction('research')}
                          disabled={isProcessing}
                          className="flex items-center gap-1"
                        >
                          <Search className="h-4 w-4" />
                          Research
                        </Button>
                        <Button 
                          variant="outline" 
                          size="sm" 
                          onClick={() => handleQuickAction('resolve')}
                          disabled={isProcessing}
                          className="flex items-center gap-1"
                        >
                          <Zap className="h-4 w-4" />
                          Resolve
                        </Button>
                        <Button 
                          variant="outline" 
                          size="sm" 
                          onClick={() => handleQuickAction('escalate')}
                          disabled={isProcessing}
                          className="flex items-center gap-1"
                        >
                          <AlertTriangle className="h-4 w-4" />
                          Escalate
                        </Button>
                        <Button 
                          variant="outline" 
                          size="sm" 
                          onClick={() => handleQuickAction('analyze')}
                          disabled={isProcessing}
                          className="flex items-center gap-1"
                        >
                          <Settings className="h-4 w-4" />
                          Analyze
                        </Button>
                      </>
                    )}
                  </div>
                </div>

                <Separator className="mb-4 flex-shrink-0" />

                {/* ISOLATED CHAT MESSAGES CONTAINER - FIXED HEIGHT */}
                <div 
                  ref={chatMessagesRef}
                  className="flex-1 overflow-y-auto border rounded-md bg-background/50 p-4 min-h-0"
                  style={{ 
                    height: 'calc(100vh - 450px)', // Fixed height
                    maxHeight: '450px' // Maximum height
                  }}
                >
                  <div className="space-y-4">
                    {chatMessages.map((message) => (
                      <div key={message.id} className={`flex ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}>
                        <div className={`max-w-[80%] ${message.type === 'user' ? 'bg-primary text-primary-foreground' : 'bg-muted'} rounded-lg p-3`}>
                          <div className="text-sm">
                            {typeof message.message === 'string' ? (
                              <div className="whitespace-pre-wrap">{message.message}</div>
                            ) : (
                              message.message
                            )}
                          </div>
                          <div className="text-xs opacity-70 mt-2">
                            {format(message.timestamp, 'HH:mm')}
                          </div>
                          {shouldShowActionButtons(message) && (
                            <div className="mt-3 space-y-2">
                              <Button 
                                size="sm" 
                                variant="default"
                                onClick={handleApplyResolution}
                                className="w-full justify-start bg-primary hover:bg-primary/90 text-primary-foreground"
                              >
                                <CheckCircle className="h-4 w-4 mr-2" />
                                Apply Resolution
                              </Button>
                              <Button 
                                size="sm" 
                                variant="outline"
                                onClick={handleAssignTicket}
                                className="w-full justify-start"
                              >
                                <UserPlus className="h-4 w-4 mr-2" />
                                Assign Ticket
                              </Button>
                            </div>
                          )}
                        </div>
                      </div>
                    ))}
                    {isProcessing && (
                      <div className="flex justify-start">
                        <div className="bg-muted rounded-lg p-3">
                          <div className="flex items-center gap-2">
                            <Bot className="h-4 w-4 animate-spin" />
                            <span className="text-sm">AI is thinking...</span>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                </div>

                {/* Chat Input */}
                <div className="mt-4 flex-shrink-0">
                  <div className="flex gap-2">
                    <Input
                      placeholder="Ask AI about this incident..."
                      value={inputMessage}
                      onChange={(e) => setInputMessage(e.target.value)}
                      onKeyPress={(e) => e.key === 'Enter' && handleSendMessage()}
                      disabled={isProcessing}
                    />
                    <Button 
                      onClick={handleSendMessage} 
                      disabled={!inputMessage.trim() || isProcessing}
                      size="icon"
                    >
                      <Send className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </TabsContent>

              <TabsContent value="resolution" className="flex-1 p-4 space-y-4 overflow-y-auto">
                <h3 className="text-lg font-semibold">Resolution Notes</h3>
                
                <div>
                  <label className="text-sm font-medium mb-2 block">Notes</label>
                  <Textarea
                    placeholder="Enter resolution notes or use 'Copy Chat to Resolution Notes' button to populate from AI chat..."
                    value={resolutionNotes}
                    onChange={(e) => setResolutionNotes(e.target.value)}
                    className="min-h-[300px]"
                  />
                </div>
                
                <div className="flex gap-2">
                  <Button className="flex items-center gap-2" onClick={handleMarkResolved}>
                    <CheckCircle className="h-4 w-4" />
                    Apply & Mark Resolved
                  </Button>
                  <Button variant="outline" className="flex items-center gap-2" onClick={handleCopyToClipboard}>
                    <Copy className="h-4 w-4" />
                    Copy to Clipboard
                  </Button>
                  <Button variant="outline" className="flex items-center gap-2" onClick={handleExportReport}>
                    <Download className="h-4 w-4" />
                    Export Report
                  </Button>
                </div>
              </TabsContent>

              <TabsContent value="jira" className="flex-1 p-4">
                <div className="space-y-4">
                  <div className="flex items-center gap-2">
                    <ExternalLink className="h-5 w-5" />
                    <h3 className="text-lg font-semibold">JIRA Integration</h3>
                  </div>
                  
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-sm">Create JIRA Ticket</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div>
                        <label className="text-sm font-medium">Assignee</label>
                        <Select value={jiraAssignee} onValueChange={setJiraAssignee}>
                          <SelectTrigger>
                            <SelectValue placeholder="Select assignee" />
                          </SelectTrigger>
                          <SelectContent>
                            {isSafetyIncident() ? (
                              // Safety-specific assignees
                              <>
                                <SelectItem value="safety.martinez">🛡️ Safety Officer Martinez (Primary Response)</SelectItem>
                                <SelectItem value="safety.director.johnson">🛡️ Safety Director Johnson (Supervisor)</SelectItem>
                                <SelectItem value="safety.manager.thompson">🛡️ Safety Manager Thompson (Investigation Lead)</SelectItem>
                                <SelectItem value="osha.coordinator.wilson">📋 OSHA Coordinator Wilson (Compliance)</SelectItem>
                                <SelectItem value="emergency.response.team">🚨 Emergency Response Team (Immediate Action)</SelectItem>
                                <SelectItem value="safety.engineer.davis">🔧 Safety Engineer Davis (Technical Analysis)</SelectItem>
                                <SelectItem value="safety.trainer.garcia">📚 Safety Trainer Garcia (Training & Prevention)</SelectItem>
                              </>
                            ) : (
                              // Standard assignees for other incident types
                              <>
                                <SelectItem value="john.doe">John Doe (Maintenance)</SelectItem>
                                <SelectItem value="sarah.smith">Sarah Smith (Engineering)</SelectItem>
                                <SelectItem value="mike.johnson">Mike Johnson (Operations)</SelectItem>
                                <SelectItem value="maintenance-team">Maintenance Team</SelectItem>
                              </>
                            )}
                          </SelectContent>
                        </Select>
                      </div>
                      
                      <div>
                        <label className="text-sm font-medium">Priority</label>
                        <Select value={jiraPriority} onValueChange={setJiraPriority}>
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="Critical">Critical</SelectItem>
                            <SelectItem value="High">High</SelectItem>
                            <SelectItem value="Medium">Medium</SelectItem>
                            <SelectItem value="Low">Low</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>

                      <div>
                        <label className="text-sm font-medium">Project</label>
                        <Select defaultValue="MAINT">
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="MAINT">Manufacturing Maintenance</SelectItem>
                            <SelectItem value="OPS">Operations</SelectItem>
                            <SelectItem value="ENG">Engineering</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>

                      <Button 
                        onClick={handleCreateJiraTicket} 
                        className="w-full"
                        disabled={!jiraAssignee}
                      >
                        Create JIRA Ticket
                      </Button>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader>
                      <CardTitle className="text-sm">Integration Status</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="flex items-center gap-2 text-sm">
                        <CheckCircle className="h-4 w-4 text-green-500" />
                        <span>Connected to JIRA Cloud</span>
                      </div>
                      <div className="flex items-center gap-2 text-sm mt-2">
                        <ExternalLink className="h-4 w-4 text-blue-500" />
                        <a href="#" className="text-blue-500 hover:underline">
                          View in JIRA Dashboard
                        </a>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </TabsContent>
            </Tabs>
          </div>
        </div>

        {/* Footer */}
        <div className="border-t p-4 flex justify-between items-center">
          <div className="text-sm text-muted-foreground">
            AI-powered incident resolution • Real-time collaboration
          </div>
          <div className="flex gap-2">
            <Button variant="outline" onClick={onClose}>
              Close
            </Button>
            <Button className="flex items-center gap-2">
              <Share className="h-4 w-4" />
              Share Session
            </Button>
          </div>
        </div>

        {/* Toast Container */}
        <div className="fixed bottom-4 right-4 z-50 space-y-2">
          {toasts.map((toast) => (
            <div
              key={toast.id}
              className={`
                flex items-center gap-2 px-4 py-3 rounded-md shadow-lg border
                ${toast.type === 'success' ? 'bg-green-50 border-green-200 text-green-800' : ''}
                ${toast.type === 'error' ? 'bg-red-50 border-red-200 text-red-800' : ''}
                ${toast.type === 'info' ? 'bg-blue-50 border-blue-200 text-blue-800' : ''}
                animate-in slide-in-from-right-4 duration-300
              `}
            >
              {toast.type === 'success' && <CheckCircle className="h-4 w-4 text-green-600" />}
              {toast.type === 'error' && <AlertTriangle className="h-4 w-4 text-red-600" />}
              {toast.type === 'info' && <FileText className="h-4 w-4 text-blue-600" />}
              <span className="text-sm font-medium">{toast.message}</span>
              <button
                onClick={() => removeToast(toast.id)}
                className="ml-2 p-1 hover:bg-black/10 rounded"
              >
                <X className="h-3 w-3" />
              </button>
            </div>
          ))}
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default AIResolutionModal; 