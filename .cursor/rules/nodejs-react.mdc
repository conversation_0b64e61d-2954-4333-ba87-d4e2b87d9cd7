---
description: When developing Node backend with React - any design changes or code changes 
globs: 
alwaysApply: false
---
Tech Stack:

Backend: Node.js with Express.js  
Database: MongoDB with Mongoose ODM  
Frontend: React.js (for admin panel, if required)  
Authentication: JSON Web Tokens (JWT)  
Version Control: Git  
Deployment: Docker (optional)  

Precision in User Requirements:

Strictly adhere to specified user flow and game rules.  

Strategy: 

Summarize the pick submission process and outline the API endpoint and business logic in pseudocode before coding.  

Strategic Planning with Pseudocode:

Begin each feature with detailed pseudocode.  
Example: Provide pseudocode for the weekly scoring process, detailing steps from game result input to entry status updates.  

Code Quality:

Ensure secure, efficient code following RESTful API best practices.  
Implement proper error handling and input validation.  

User Flow:

Users browse available Pools  
Submit up to 3 Requests per Pool  
Complete payment for Requests  
Admin approves/rejects Requests  
Approved Requests become Entries  

Entry Management:

Each user can have up to 3 Entries per Pool  
Entries are numbered 1, 2, 3  
Picks are made and tracked separately for each Entry  

Pick Management:
Users make Picks for each Entry separately  
Picks can be updated until deadline (game start or 1PM Sunday of the current week of the pick)  

Scoring and Ranking:

Picks scored after games complete  
Win: Entry moves to next week  
Loss: Entry eliminated from Pool  
Each Entry ranked separately in Pool standings  

Results and Standings:

Users view Picks/scores for each Entry separately  
Pool standings show all Entries (multiple per User possible)  
Pool members can view all Picks after scoring  

Key Implementation Points:

Limit Requests to 3 per User per Pool  
Track Requests and Entries separately (numbered 1, 2, 3)  
Implement payment status tracking in Request model  
Create Entry only after admin approval and payment completion  
Admin interface for managing and approving Requests  
Implement state transitions (Request: pending -> approved -> Entry created)  
