---
description: 
globs: 
alwaysApply: false
---
When generating new Python code:
- Avoid using Pathlib, prefer os.path if similar functionality is needed.
- Avoid passing shell=True commands to subprocess.run, instead pass commands as lists like ['cmd', 'arg1', 'arg2'].
- Prefer the niquests or httpx libraries over requests.
- Prefer the click library over argparse.
- Format CLI output using rich library.
- Avoid capturing "Exception" unless you are able to gracefully resolve the raised exception. Simplify the code by normally just letting python crash, allowing the default Traceback handling to kick in.
- Follow PEP8 and Black style guidelines. Wrap text at 120 characters (line length).
- Write function bodies in a concise style: minimal whitespace, minimal comments, no docstrings.
- Write mypy-style type hints for function arguments & return values.
- Install new dependencies for a project by running the command `uv add {dependency name}`. This command wil auto-update pyproject.toml for you. No requirements.txt. However, if you are just editing a script, and the 2nd or 3rd line of the file says "# dependencies = [", then register new dependencies for that script by directly appending to that list at the top of the file (this a new python feature!).
- Use pytest for tests. Create a dedicated top-level "tests" folder if it does not exist already, and place ALL tests there. Run the tests with `uv run pytest`. 
- Think about code factoring concerns and architecture concerns while generating code. Avoid duplicating code if the same or similar code already exists elsewhere, create modular self-contained re-usable cohesive functions, avoid tight coupling between distinct areas of the code, etc. Feel free to go back and refactor pre-existing code when needed as a pre-requisite for implementing a cleaner solution to the problem at hand.
